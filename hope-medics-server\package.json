{"name": "hope-medics-server", "version": "1.0.0", "description": "Backend API for Hope Medics hospital management system", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["hospital", "medical", "appointment", "healthcare"], "author": "Hope Medics Team", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1"}, "devDependencies": {"nodemon": "^3.1.10"}}