{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add auth token to requests if available\napi.interceptors.request.use(config => {\n  const user = JSON.parse(localStorage.getItem('user') || '{}');\n  if (user.token) {\n    config.headers.Authorization = `Bearer ${user.token}`;\n  }\n  return config;\n});\n\n// Auth API\nexport const authAPI = {\n  login: credentials => api.post('/auth/login', credentials),\n  register: userData => api.post('/auth/register', userData)\n};\n\n// Hospitals API\nexport const hospitalsAPI = {\n  getAll: () => api.get('/hospitals'),\n  getById: id => api.get(`/hospitals/${id}`),\n  create: hospitalData => api.post('/hospitals', hospitalData),\n  update: (id, hospitalData) => api.put(`/hospitals/${id}`, hospitalData),\n  delete: id => api.delete(`/hospitals/${id}`)\n};\n\n// Doctors API\nexport const doctorsAPI = {\n  getAll: () => api.get('/doctors'),\n  getById: id => api.get(`/doctors/${id}`),\n  getByHospital: hospitalId => api.get(`/doctors/hospital/${hospitalId}`),\n  create: doctorData => api.post('/doctors', doctorData),\n  update: (id, doctorData) => api.put(`/doctors/${id}`, doctorData),\n  delete: id => api.delete(`/doctors/${id}`)\n};\n\n// Appointments API\nexport const appointmentsAPI = {\n  getAll: () => api.get('/appointments'),\n  getById: id => api.get(`/appointments/${id}`),\n  getByPatient: patientId => api.get(`/appointments/patient/${patientId}`),\n  getByDoctor: doctorId => api.get(`/appointments/doctor/${doctorId}`),\n  create: appointmentData => api.post('/appointments', appointmentData),\n  update: (id, appointmentData) => api.put(`/appointments/${id}`, appointmentData),\n  delete: id => api.delete(`/appointments/${id}`),\n  manualBooking: appointmentData => api.post('/reception/manual', appointmentData)\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "user", "JSON", "parse", "localStorage", "getItem", "token", "Authorization", "authAPI", "login", "credentials", "post", "register", "userData", "hospitalsAPI", "getAll", "get", "getById", "id", "hospitalData", "update", "put", "delete", "doctorsAPI", "getByHospital", "hospitalId", "<PERSON><PERSON><PERSON>", "appointmentsAPI", "getByPatient", "patientId", "getByDoctor", "doctorId", "appointmentData", "manualBooking"], "sources": ["D:/WebProject/hopeMedics/hopemedics/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add auth token to requests if available\napi.interceptors.request.use((config) => {\n  const user = JSON.parse(localStorage.getItem('user') || '{}');\n  if (user.token) {\n    config.headers.Authorization = `Bearer ${user.token}`;\n  }\n  return config;\n});\n\n// Auth API\nexport const authAPI = {\n  login: (credentials) => api.post('/auth/login', credentials),\n  register: (userData) => api.post('/auth/register', userData),\n};\n\n// Hospitals API\nexport const hospitalsAPI = {\n  getAll: () => api.get('/hospitals'),\n  getById: (id) => api.get(`/hospitals/${id}`),\n  create: (hospitalData) => api.post('/hospitals', hospitalData),\n  update: (id, hospitalData) => api.put(`/hospitals/${id}`, hospitalData),\n  delete: (id) => api.delete(`/hospitals/${id}`),\n};\n\n// Doctors API\nexport const doctorsAPI = {\n  getAll: () => api.get('/doctors'),\n  getById: (id) => api.get(`/doctors/${id}`),\n  getByHospital: (hospitalId) => api.get(`/doctors/hospital/${hospitalId}`),\n  create: (doctorData) => api.post('/doctors', doctorData),\n  update: (id, doctorData) => api.put(`/doctors/${id}`, doctorData),\n  delete: (id) => api.delete(`/doctors/${id}`),\n};\n\n// Appointments API\nexport const appointmentsAPI = {\n  getAll: () => api.get('/appointments'),\n  getById: (id) => api.get(`/appointments/${id}`),\n  getByPatient: (patientId) => api.get(`/appointments/patient/${patientId}`),\n  getByDoctor: (doctorId) => api.get(`/appointments/doctor/${doctorId}`),\n  create: (appointmentData) => api.post('/appointments', appointmentData),\n  update: (id, appointmentData) => api.put(`/appointments/${id}`, appointmentData),\n  delete: (id) => api.delete(`/appointments/${id}`),\n  manualBooking: (appointmentData) => api.post('/reception/manual', appointmentData),\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAAEC,MAAM,IAAK;EACvC,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;EAC7D,IAAIJ,IAAI,CAACK,KAAK,EAAE;IACdN,MAAM,CAACJ,OAAO,CAACW,aAAa,GAAG,UAAUN,IAAI,CAACK,KAAK,EAAE;EACvD;EACA,OAAON,MAAM;AACf,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMQ,OAAO,GAAG;EACrBC,KAAK,EAAGC,WAAW,IAAKjB,GAAG,CAACkB,IAAI,CAAC,aAAa,EAAED,WAAW,CAAC;EAC5DE,QAAQ,EAAGC,QAAQ,IAAKpB,GAAG,CAACkB,IAAI,CAAC,gBAAgB,EAAEE,QAAQ;AAC7D,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1BC,MAAM,EAAEA,CAAA,KAAMtB,GAAG,CAACuB,GAAG,CAAC,YAAY,CAAC;EACnCC,OAAO,EAAGC,EAAE,IAAKzB,GAAG,CAACuB,GAAG,CAAC,cAAcE,EAAE,EAAE,CAAC;EAC5CxB,MAAM,EAAGyB,YAAY,IAAK1B,GAAG,CAACkB,IAAI,CAAC,YAAY,EAAEQ,YAAY,CAAC;EAC9DC,MAAM,EAAEA,CAACF,EAAE,EAAEC,YAAY,KAAK1B,GAAG,CAAC4B,GAAG,CAAC,cAAcH,EAAE,EAAE,EAAEC,YAAY,CAAC;EACvEG,MAAM,EAAGJ,EAAE,IAAKzB,GAAG,CAAC6B,MAAM,CAAC,cAAcJ,EAAE,EAAE;AAC/C,CAAC;;AAED;AACA,OAAO,MAAMK,UAAU,GAAG;EACxBR,MAAM,EAAEA,CAAA,KAAMtB,GAAG,CAACuB,GAAG,CAAC,UAAU,CAAC;EACjCC,OAAO,EAAGC,EAAE,IAAKzB,GAAG,CAACuB,GAAG,CAAC,YAAYE,EAAE,EAAE,CAAC;EAC1CM,aAAa,EAAGC,UAAU,IAAKhC,GAAG,CAACuB,GAAG,CAAC,qBAAqBS,UAAU,EAAE,CAAC;EACzE/B,MAAM,EAAGgC,UAAU,IAAKjC,GAAG,CAACkB,IAAI,CAAC,UAAU,EAAEe,UAAU,CAAC;EACxDN,MAAM,EAAEA,CAACF,EAAE,EAAEQ,UAAU,KAAKjC,GAAG,CAAC4B,GAAG,CAAC,YAAYH,EAAE,EAAE,EAAEQ,UAAU,CAAC;EACjEJ,MAAM,EAAGJ,EAAE,IAAKzB,GAAG,CAAC6B,MAAM,CAAC,YAAYJ,EAAE,EAAE;AAC7C,CAAC;;AAED;AACA,OAAO,MAAMS,eAAe,GAAG;EAC7BZ,MAAM,EAAEA,CAAA,KAAMtB,GAAG,CAACuB,GAAG,CAAC,eAAe,CAAC;EACtCC,OAAO,EAAGC,EAAE,IAAKzB,GAAG,CAACuB,GAAG,CAAC,iBAAiBE,EAAE,EAAE,CAAC;EAC/CU,YAAY,EAAGC,SAAS,IAAKpC,GAAG,CAACuB,GAAG,CAAC,yBAAyBa,SAAS,EAAE,CAAC;EAC1EC,WAAW,EAAGC,QAAQ,IAAKtC,GAAG,CAACuB,GAAG,CAAC,wBAAwBe,QAAQ,EAAE,CAAC;EACtErC,MAAM,EAAGsC,eAAe,IAAKvC,GAAG,CAACkB,IAAI,CAAC,eAAe,EAAEqB,eAAe,CAAC;EACvEZ,MAAM,EAAEA,CAACF,EAAE,EAAEc,eAAe,KAAKvC,GAAG,CAAC4B,GAAG,CAAC,iBAAiBH,EAAE,EAAE,EAAEc,eAAe,CAAC;EAChFV,MAAM,EAAGJ,EAAE,IAAKzB,GAAG,CAAC6B,MAAM,CAAC,iBAAiBJ,EAAE,EAAE,CAAC;EACjDe,aAAa,EAAGD,eAAe,IAAKvC,GAAG,CAACkB,IAAI,CAAC,mBAAmB,EAAEqB,eAAe;AACnF,CAAC;AAED,eAAevC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}