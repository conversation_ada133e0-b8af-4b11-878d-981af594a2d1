const express = require('express');
const router = express.Router();
const appointmentController = require('../controllers/appointmentController');
const { 
  authenticate, 
  requireAdmin, 
  requireAdminOrReceptionist,
  requireAdminOrDoctor 
} = require('../middleware/auth');

// Protected routes - require authentication
router.get('/', authenticate, appointmentController.getAllAppointments);
router.get('/:id', authenticate, appointmentController.getAppointmentById);
router.get('/patient/:patientId', authenticate, appointmentController.getAppointmentsByPatient);
router.get('/doctor/:doctorId', authenticate, appointmentController.getAppointmentsByDoctor);

// Patient can book appointments
router.post('/', authenticate, appointmentController.createAppointment);

// Reception manual booking
router.post('/reception/manual', authenticate, requireAdminOrReceptionist, appointmentController.manualBooking);

// Update appointment (admin, doctor, or patient who owns it)
router.put('/:id', authenticate, appointmentController.updateAppointment);
router.patch('/:id/status', authenticate, requireAdminOrDoctor, appointmentController.updateAppointmentStatus);

// Delete appointment (admin or patient who owns it)
router.delete('/:id', authenticate, appointmentController.deleteAppointment);

module.exports = router;
