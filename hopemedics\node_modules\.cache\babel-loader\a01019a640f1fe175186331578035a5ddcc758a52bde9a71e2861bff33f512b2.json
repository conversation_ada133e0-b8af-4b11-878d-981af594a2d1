{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\hopeMedics\\\\hopemedics\\\\src\\\\pages\\\\HospitalDetails.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, Link } from 'react-router-dom';\nimport { hospitalsAPI, doctorsAPI } from '../services/api';\nimport DoctorCard from '../components/DoctorCard';\nimport Navbar from '../components/Navbar';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HospitalDetails = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const {\n    isAuthenticated\n  } = useAuth();\n  const [hospital, setHospital] = useState(null);\n  const [doctors, setDoctors] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetchHospitalDetails();\n    fetchDoctors();\n  }, [id]);\n  const fetchHospitalDetails = async () => {\n    try {\n      const response = await hospitalsAPI.getById(id);\n      setHospital(response.data);\n    } catch (err) {\n      console.error('Error fetching hospital details:', err);\n      // Mock data for development\n      setHospital(getMockHospital(id));\n    }\n  };\n  const fetchDoctors = async () => {\n    try {\n      const response = await doctorsAPI.getByHospital(id);\n      setDoctors(response.data);\n    } catch (err) {\n      console.error('Error fetching doctors:', err);\n      // Mock data for development\n      setDoctors(getMockDoctors(id));\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getMockHospital = hospitalId => {\n    const hospitals = {\n      '1': {\n        id: 1,\n        name: \"City General Hospital\",\n        address: \"123 Main St, Downtown\",\n        contact: \"(555) 123-4567\",\n        bedPricePerDay: 150.00,\n        photos: JSON.stringify([\"https://via.placeholder.com/800x400?text=City+General+Hospital+Main\", \"https://via.placeholder.com/400x300?text=Emergency+Room\", \"https://via.placeholder.com/400x300?text=Patient+Room\"]),\n        services: JSON.stringify([\"Emergency Care\", \"Surgery\", \"Cardiology\", \"Pediatrics\", \"Radiology\", \"Laboratory\"])\n      },\n      '2': {\n        id: 2,\n        name: \"Hope Medical Center\",\n        address: \"456 Oak Ave, Midtown\",\n        contact: \"(555) 987-6543\",\n        bedPricePerDay: 200.00,\n        photos: JSON.stringify([\"https://via.placeholder.com/800x400?text=Hope+Medical+Center\", \"https://via.placeholder.com/400x300?text=Surgery+Suite\", \"https://via.placeholder.com/400x300?text=ICU\"]),\n        services: JSON.stringify([\"Oncology\", \"Neurology\", \"Orthopedics\", \"Radiology\", \"Pharmacy\", \"Rehabilitation\"])\n      },\n      '3': {\n        id: 3,\n        name: \"Sunrise Healthcare\",\n        address: \"789 Pine Rd, Uptown\",\n        contact: \"(555) 456-7890\",\n        bedPricePerDay: 175.00,\n        photos: JSON.stringify([\"https://via.placeholder.com/800x400?text=Sunrise+Healthcare\", \"https://via.placeholder.com/400x300?text=Maternity+Ward\", \"https://via.placeholder.com/400x300?text=Therapy+Center\"]),\n        services: JSON.stringify([\"Maternity\", \"Dermatology\", \"Psychiatry\", \"Physical Therapy\", \"Nutrition\", \"Wellness\"])\n      }\n    };\n    return hospitals[hospitalId] || hospitals['1'];\n  };\n  const getMockDoctors = hospitalId => {\n    const doctorsByHospital = {\n      '1': [{\n        id: 1,\n        name: \"Sarah Johnson\",\n        specialty: \"Cardiology\",\n        hospitalId: 1\n      }, {\n        id: 2,\n        name: \"Michael Chen\",\n        specialty: \"Emergency Medicine\",\n        hospitalId: 1\n      }, {\n        id: 3,\n        name: \"Emily Davis\",\n        specialty: \"Pediatrics\",\n        hospitalId: 1\n      }],\n      '2': [{\n        id: 4,\n        name: \"Robert Wilson\",\n        specialty: \"Oncology\",\n        hospitalId: 2\n      }, {\n        id: 5,\n        name: \"Lisa Anderson\",\n        specialty: \"Neurology\",\n        hospitalId: 2\n      }, {\n        id: 6,\n        name: \"David Brown\",\n        specialty: \"Orthopedics\",\n        hospitalId: 2\n      }],\n      '3': [{\n        id: 7,\n        name: \"Jennifer Taylor\",\n        specialty: \"Obstetrics & Gynecology\",\n        hospitalId: 3\n      }, {\n        id: 8,\n        name: \"Mark Thompson\",\n        specialty: \"Dermatology\",\n        hospitalId: 3\n      }, {\n        id: 9,\n        name: \"Amanda White\",\n        specialty: \"Psychiatry\",\n        hospitalId: 3\n      }]\n    };\n    return doctorsByHospital[hospitalId] || [];\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center items-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this);\n  }\n  if (!hospital) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900 mb-4\",\n            children: \"Hospital Not Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"text-primary-600 hover:text-primary-700\",\n            children: \"Back to Hospitals\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this);\n  }\n  const photos = hospital.photos ? JSON.parse(hospital.photos) : [];\n  const services = hospital.services ? JSON.parse(hospital.services) : [];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: \"inline-flex items-center text-primary-600 hover:text-primary-700 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-5 h-5 mr-2\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 20 20\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\",\n            clipRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), \"Back to Hospitals\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md overflow-hidden mb-8\",\n        children: [photos.length > 0 && /*#__PURE__*/_jsxDEV(\"img\", {\n          src: photos[0],\n          alt: hospital.name,\n          className: \"w-full h-64 object-cover\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-start mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                children: hospital.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-1\",\n                children: hospital.address\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: hospital.contact\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-right\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-primary-600\",\n                children: [\"$\", hospital.bedPricePerDay, \"/day\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Bed Price\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), isAuthenticated && /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/book\",\n            state: {\n              hospitalId: hospital.id,\n              hospitalName: hospital.name\n            },\n            className: \"bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-md font-medium inline-block transition-colors duration-200\",\n            children: \"Book Appointment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-gray-900 mb-4\",\n              children: \"Services\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: services.map((service, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-secondary-500 mr-3\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-700\",\n                  children: service\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), photos.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6 mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-gray-900 mb-4\",\n              children: \"Gallery\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-2\",\n              children: photos.slice(1).map((photo, index) => /*#__PURE__*/_jsxDEV(\"img\", {\n                src: photo,\n                alt: `${hospital.name} ${index + 1}`,\n                className: \"w-full h-24 object-cover rounded\"\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-gray-900 mb-6\",\n              children: \"Available Doctors\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), doctors.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-500\",\n              children: \"No doctors available at this hospital.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n              children: doctors.map(doctor => /*#__PURE__*/_jsxDEV(DoctorCard, {\n                doctor: doctor\n              }, doctor.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 5\n  }, this);\n};\n_s(HospitalDetails, \"pMe4is+gGKSTcViCkhEutz+C1dI=\", false, function () {\n  return [useParams, useAuth];\n});\n_c = HospitalDetails;\nexport default HospitalDetails;\nvar _c;\n$RefreshReg$(_c, \"HospitalDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Link", "hospitalsAPI", "doctorsAPI", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "useAuth", "jsxDEV", "_jsxDEV", "HospitalDetails", "_s", "id", "isAuthenticated", "hospital", "setHospital", "doctors", "setDoctors", "loading", "setLoading", "error", "setError", "fetchHospitalDetails", "fetchDoctors", "response", "getById", "data", "err", "console", "getMockHospital", "getByHospital", "getMockDoctors", "hospitalId", "hospitals", "name", "address", "contact", "bedPricePerDay", "photos", "JSON", "stringify", "services", "doctorsByHospital", "specialty", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "parse", "fill", "viewBox", "fillRule", "d", "clipRule", "length", "src", "alt", "state", "hospitalName", "map", "service", "index", "slice", "photo", "doctor", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/hopeMedics/hopemedics/src/pages/HospitalDetails.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { usePara<PERSON>, Link } from 'react-router-dom';\nimport { hospitalsAPI, doctorsAPI } from '../services/api';\nimport DoctorCard from '../components/DoctorCard';\nimport Navbar from '../components/Navbar';\nimport { useAuth } from '../context/AuthContext';\n\nconst HospitalDetails = () => {\n  const { id } = useParams();\n  const { isAuthenticated } = useAuth();\n  const [hospital, setHospital] = useState(null);\n  const [doctors, setDoctors] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    fetchHospitalDetails();\n    fetchDoctors();\n  }, [id]);\n\n  const fetchHospitalDetails = async () => {\n    try {\n      const response = await hospitalsAPI.getById(id);\n      setHospital(response.data);\n    } catch (err) {\n      console.error('Error fetching hospital details:', err);\n      // Mock data for development\n      setHospital(getMockHospital(id));\n    }\n  };\n\n  const fetchDoctors = async () => {\n    try {\n      const response = await doctorsAPI.getByHospital(id);\n      setDoctors(response.data);\n    } catch (err) {\n      console.error('Error fetching doctors:', err);\n      // Mock data for development\n      setDoctors(getMockDoctors(id));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getMockHospital = (hospitalId) => {\n    const hospitals = {\n      '1': {\n        id: 1,\n        name: \"City General Hospital\",\n        address: \"123 Main St, Downtown\",\n        contact: \"(555) 123-4567\",\n        bedPricePerDay: 150.00,\n        photos: JSON.stringify([\n          \"https://via.placeholder.com/800x400?text=City+General+Hospital+Main\",\n          \"https://via.placeholder.com/400x300?text=Emergency+Room\",\n          \"https://via.placeholder.com/400x300?text=Patient+Room\"\n        ]),\n        services: JSON.stringify([\"Emergency Care\", \"Surgery\", \"Cardiology\", \"Pediatrics\", \"Radiology\", \"Laboratory\"])\n      },\n      '2': {\n        id: 2,\n        name: \"Hope Medical Center\",\n        address: \"456 Oak Ave, Midtown\",\n        contact: \"(555) 987-6543\",\n        bedPricePerDay: 200.00,\n        photos: JSON.stringify([\n          \"https://via.placeholder.com/800x400?text=Hope+Medical+Center\",\n          \"https://via.placeholder.com/400x300?text=Surgery+Suite\",\n          \"https://via.placeholder.com/400x300?text=ICU\"\n        ]),\n        services: JSON.stringify([\"Oncology\", \"Neurology\", \"Orthopedics\", \"Radiology\", \"Pharmacy\", \"Rehabilitation\"])\n      },\n      '3': {\n        id: 3,\n        name: \"Sunrise Healthcare\",\n        address: \"789 Pine Rd, Uptown\",\n        contact: \"(555) 456-7890\",\n        bedPricePerDay: 175.00,\n        photos: JSON.stringify([\n          \"https://via.placeholder.com/800x400?text=Sunrise+Healthcare\",\n          \"https://via.placeholder.com/400x300?text=Maternity+Ward\",\n          \"https://via.placeholder.com/400x300?text=Therapy+Center\"\n        ]),\n        services: JSON.stringify([\"Maternity\", \"Dermatology\", \"Psychiatry\", \"Physical Therapy\", \"Nutrition\", \"Wellness\"])\n      }\n    };\n    return hospitals[hospitalId] || hospitals['1'];\n  };\n\n  const getMockDoctors = (hospitalId) => {\n    const doctorsByHospital = {\n      '1': [\n        { id: 1, name: \"Sarah Johnson\", specialty: \"Cardiology\", hospitalId: 1 },\n        { id: 2, name: \"Michael Chen\", specialty: \"Emergency Medicine\", hospitalId: 1 },\n        { id: 3, name: \"Emily Davis\", specialty: \"Pediatrics\", hospitalId: 1 }\n      ],\n      '2': [\n        { id: 4, name: \"Robert Wilson\", specialty: \"Oncology\", hospitalId: 2 },\n        { id: 5, name: \"Lisa Anderson\", specialty: \"Neurology\", hospitalId: 2 },\n        { id: 6, name: \"David Brown\", specialty: \"Orthopedics\", hospitalId: 2 }\n      ],\n      '3': [\n        { id: 7, name: \"Jennifer Taylor\", specialty: \"Obstetrics & Gynecology\", hospitalId: 3 },\n        { id: 8, name: \"Mark Thompson\", specialty: \"Dermatology\", hospitalId: 3 },\n        { id: 9, name: \"Amanda White\", specialty: \"Psychiatry\", hospitalId: 3 }\n      ]\n    };\n    return doctorsByHospital[hospitalId] || [];\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navbar />\n        <div className=\"flex justify-center items-center h-64\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!hospital) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navbar />\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"text-center\">\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Hospital Not Found</h1>\n            <Link to=\"/\" className=\"text-primary-600 hover:text-primary-700\">\n              Back to Hospitals\n            </Link>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const photos = hospital.photos ? JSON.parse(hospital.photos) : [];\n  const services = hospital.services ? JSON.parse(hospital.services) : [];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n      \n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Back Button */}\n        <Link \n          to=\"/\" \n          className=\"inline-flex items-center text-primary-600 hover:text-primary-700 mb-6\"\n        >\n          <svg className=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clipRule=\"evenodd\" />\n          </svg>\n          Back to Hospitals\n        </Link>\n\n        {/* Hospital Header */}\n        <div className=\"bg-white rounded-lg shadow-md overflow-hidden mb-8\">\n          {photos.length > 0 && (\n            <img\n              src={photos[0]}\n              alt={hospital.name}\n              className=\"w-full h-64 object-cover\"\n            />\n          )}\n          \n          <div className=\"p-6\">\n            <div className=\"flex justify-between items-start mb-4\">\n              <div>\n                <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">{hospital.name}</h1>\n                <p className=\"text-gray-600 mb-1\">{hospital.address}</p>\n                <p className=\"text-gray-600\">{hospital.contact}</p>\n              </div>\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-primary-600\">\n                  ${hospital.bedPricePerDay}/day\n                </div>\n                <p className=\"text-sm text-gray-500\">Bed Price</p>\n              </div>\n            </div>\n\n            {isAuthenticated && (\n              <Link\n                to=\"/book\"\n                state={{ hospitalId: hospital.id, hospitalName: hospital.name }}\n                className=\"bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-md font-medium inline-block transition-colors duration-200\"\n              >\n                Book Appointment\n              </Link>\n            )}\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Services */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <h2 className=\"text-xl font-bold text-gray-900 mb-4\">Services</h2>\n              <div className=\"space-y-2\">\n                {services.map((service, index) => (\n                  <div key={index} className=\"flex items-center\">\n                    <svg className=\"w-5 h-5 text-secondary-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                    <span className=\"text-gray-700\">{service}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Photo Gallery */}\n            {photos.length > 1 && (\n              <div className=\"bg-white rounded-lg shadow-md p-6 mt-6\">\n                <h2 className=\"text-xl font-bold text-gray-900 mb-4\">Gallery</h2>\n                <div className=\"grid grid-cols-2 gap-2\">\n                  {photos.slice(1).map((photo, index) => (\n                    <img\n                      key={index}\n                      src={photo}\n                      alt={`${hospital.name} ${index + 1}`}\n                      className=\"w-full h-24 object-cover rounded\"\n                    />\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Doctors */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <h2 className=\"text-xl font-bold text-gray-900 mb-6\">Available Doctors</h2>\n              {doctors.length === 0 ? (\n                <p className=\"text-gray-500\">No doctors available at this hospital.</p>\n              ) : (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  {doctors.map(doctor => (\n                    <DoctorCard key={doctor.id} doctor={doctor} />\n                  ))}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default HospitalDetails;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,QAAQ,kBAAkB;AAClD,SAASC,YAAY,EAAEC,UAAU,QAAQ,iBAAiB;AAC1D,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC;EAAG,CAAC,GAAGX,SAAS,CAAC,CAAC;EAC1B,MAAM;IAAEY;EAAgB,CAAC,GAAGN,OAAO,CAAC,CAAC;EACrC,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACdsB,oBAAoB,CAAC,CAAC;IACtBC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACX,EAAE,CAAC,CAAC;EAER,MAAMU,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMrB,YAAY,CAACsB,OAAO,CAACb,EAAE,CAAC;MAC/CG,WAAW,CAACS,QAAQ,CAACE,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACR,KAAK,CAAC,kCAAkC,EAAEO,GAAG,CAAC;MACtD;MACAZ,WAAW,CAACc,eAAe,CAACjB,EAAE,CAAC,CAAC;IAClC;EACF,CAAC;EAED,MAAMW,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMpB,UAAU,CAAC0B,aAAa,CAAClB,EAAE,CAAC;MACnDK,UAAU,CAACO,QAAQ,CAACE,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACR,KAAK,CAAC,yBAAyB,EAAEO,GAAG,CAAC;MAC7C;MACAV,UAAU,CAACc,cAAc,CAACnB,EAAE,CAAC,CAAC;IAChC,CAAC,SAAS;MACRO,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,eAAe,GAAIG,UAAU,IAAK;IACtC,MAAMC,SAAS,GAAG;MAChB,GAAG,EAAE;QACHrB,EAAE,EAAE,CAAC;QACLsB,IAAI,EAAE,uBAAuB;QAC7BC,OAAO,EAAE,uBAAuB;QAChCC,OAAO,EAAE,gBAAgB;QACzBC,cAAc,EAAE,MAAM;QACtBC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAAC,CACrB,qEAAqE,EACrE,yDAAyD,EACzD,uDAAuD,CACxD,CAAC;QACFC,QAAQ,EAAEF,IAAI,CAACC,SAAS,CAAC,CAAC,gBAAgB,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC;MAC/G,CAAC;MACD,GAAG,EAAE;QACH5B,EAAE,EAAE,CAAC;QACLsB,IAAI,EAAE,qBAAqB;QAC3BC,OAAO,EAAE,sBAAsB;QAC/BC,OAAO,EAAE,gBAAgB;QACzBC,cAAc,EAAE,MAAM;QACtBC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAAC,CACrB,8DAA8D,EAC9D,wDAAwD,EACxD,8CAA8C,CAC/C,CAAC;QACFC,QAAQ,EAAEF,IAAI,CAACC,SAAS,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,gBAAgB,CAAC;MAC9G,CAAC;MACD,GAAG,EAAE;QACH5B,EAAE,EAAE,CAAC;QACLsB,IAAI,EAAE,oBAAoB;QAC1BC,OAAO,EAAE,qBAAqB;QAC9BC,OAAO,EAAE,gBAAgB;QACzBC,cAAc,EAAE,MAAM;QACtBC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAAC,CACrB,6DAA6D,EAC7D,yDAAyD,EACzD,yDAAyD,CAC1D,CAAC;QACFC,QAAQ,EAAEF,IAAI,CAACC,SAAS,CAAC,CAAC,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,kBAAkB,EAAE,WAAW,EAAE,UAAU,CAAC;MAClH;IACF,CAAC;IACD,OAAOP,SAAS,CAACD,UAAU,CAAC,IAAIC,SAAS,CAAC,GAAG,CAAC;EAChD,CAAC;EAED,MAAMF,cAAc,GAAIC,UAAU,IAAK;IACrC,MAAMU,iBAAiB,GAAG;MACxB,GAAG,EAAE,CACH;QAAE9B,EAAE,EAAE,CAAC;QAAEsB,IAAI,EAAE,eAAe;QAAES,SAAS,EAAE,YAAY;QAAEX,UAAU,EAAE;MAAE,CAAC,EACxE;QAAEpB,EAAE,EAAE,CAAC;QAAEsB,IAAI,EAAE,cAAc;QAAES,SAAS,EAAE,oBAAoB;QAAEX,UAAU,EAAE;MAAE,CAAC,EAC/E;QAAEpB,EAAE,EAAE,CAAC;QAAEsB,IAAI,EAAE,aAAa;QAAES,SAAS,EAAE,YAAY;QAAEX,UAAU,EAAE;MAAE,CAAC,CACvE;MACD,GAAG,EAAE,CACH;QAAEpB,EAAE,EAAE,CAAC;QAAEsB,IAAI,EAAE,eAAe;QAAES,SAAS,EAAE,UAAU;QAAEX,UAAU,EAAE;MAAE,CAAC,EACtE;QAAEpB,EAAE,EAAE,CAAC;QAAEsB,IAAI,EAAE,eAAe;QAAES,SAAS,EAAE,WAAW;QAAEX,UAAU,EAAE;MAAE,CAAC,EACvE;QAAEpB,EAAE,EAAE,CAAC;QAAEsB,IAAI,EAAE,aAAa;QAAES,SAAS,EAAE,aAAa;QAAEX,UAAU,EAAE;MAAE,CAAC,CACxE;MACD,GAAG,EAAE,CACH;QAAEpB,EAAE,EAAE,CAAC;QAAEsB,IAAI,EAAE,iBAAiB;QAAES,SAAS,EAAE,yBAAyB;QAAEX,UAAU,EAAE;MAAE,CAAC,EACvF;QAAEpB,EAAE,EAAE,CAAC;QAAEsB,IAAI,EAAE,eAAe;QAAES,SAAS,EAAE,aAAa;QAAEX,UAAU,EAAE;MAAE,CAAC,EACzE;QAAEpB,EAAE,EAAE,CAAC;QAAEsB,IAAI,EAAE,cAAc;QAAES,SAAS,EAAE,YAAY;QAAEX,UAAU,EAAE;MAAE,CAAC;IAE3E,CAAC;IACD,OAAOU,iBAAiB,CAACV,UAAU,CAAC,IAAI,EAAE;EAC5C,CAAC;EAED,IAAId,OAAO,EAAE;IACX,oBACET,OAAA;MAAKmC,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtCpC,OAAA,CAACH,MAAM;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVxC,OAAA;QAAKmC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpDpC,OAAA;UAAKmC,SAAS,EAAC;QAAmE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACnC,QAAQ,EAAE;IACb,oBACEL,OAAA;MAAKmC,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtCpC,OAAA,CAACH,MAAM;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVxC,OAAA;QAAKmC,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC3DpC,OAAA;UAAKmC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BpC,OAAA;YAAImC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7ExC,OAAA,CAACP,IAAI;YAACgD,EAAE,EAAC,GAAG;YAACN,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEjE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMX,MAAM,GAAGxB,QAAQ,CAACwB,MAAM,GAAGC,IAAI,CAACY,KAAK,CAACrC,QAAQ,CAACwB,MAAM,CAAC,GAAG,EAAE;EACjE,MAAMG,QAAQ,GAAG3B,QAAQ,CAAC2B,QAAQ,GAAGF,IAAI,CAACY,KAAK,CAACrC,QAAQ,CAAC2B,QAAQ,CAAC,GAAG,EAAE;EAEvE,oBACEhC,OAAA;IAAKmC,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtCpC,OAAA,CAACH,MAAM;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEVxC,OAAA;MAAKmC,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1DpC,OAAA,CAACP,IAAI;QACHgD,EAAE,EAAC,GAAG;QACNN,SAAS,EAAC,uEAAuE;QAAAC,QAAA,gBAEjFpC,OAAA;UAAKmC,SAAS,EAAC,cAAc;UAACQ,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAR,QAAA,eACnEpC,OAAA;YAAM6C,QAAQ,EAAC,SAAS;YAACC,CAAC,EAAC,uIAAuI;YAACC,QAAQ,EAAC;UAAS;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrL,CAAC,qBAER;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAGPxC,OAAA;QAAKmC,SAAS,EAAC,oDAAoD;QAAAC,QAAA,GAChEP,MAAM,CAACmB,MAAM,GAAG,CAAC,iBAChBhD,OAAA;UACEiD,GAAG,EAAEpB,MAAM,CAAC,CAAC,CAAE;UACfqB,GAAG,EAAE7C,QAAQ,CAACoB,IAAK;UACnBU,SAAS,EAAC;QAA0B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CACF,eAEDxC,OAAA;UAAKmC,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBpC,OAAA;YAAKmC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDpC,OAAA;cAAAoC,QAAA,gBACEpC,OAAA;gBAAImC,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAE/B,QAAQ,CAACoB;cAAI;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1ExC,OAAA;gBAAGmC,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAE/B,QAAQ,CAACqB;cAAO;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxDxC,OAAA;gBAAGmC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAE/B,QAAQ,CAACsB;cAAO;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNxC,OAAA;cAAKmC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpC,OAAA;gBAAKmC,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,GAAC,GAClD,EAAC/B,QAAQ,CAACuB,cAAc,EAAC,MAC5B;cAAA;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNxC,OAAA;gBAAGmC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELpC,eAAe,iBACdJ,OAAA,CAACP,IAAI;YACHgD,EAAE,EAAC,OAAO;YACVU,KAAK,EAAE;cAAE5B,UAAU,EAAElB,QAAQ,CAACF,EAAE;cAAEiD,YAAY,EAAE/C,QAAQ,CAACoB;YAAK,CAAE;YAChEU,SAAS,EAAC,6HAA6H;YAAAC,QAAA,EACxI;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxC,OAAA;QAAKmC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDpC,OAAA;UAAKmC,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BpC,OAAA;YAAKmC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDpC,OAAA;cAAImC,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClExC,OAAA;cAAKmC,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBJ,QAAQ,CAACqB,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BvD,OAAA;gBAAiBmC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC5CpC,OAAA;kBAAKmC,SAAS,EAAC,iCAAiC;kBAACQ,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAR,QAAA,eACtFpC,OAAA;oBAAM6C,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,oHAAoH;oBAACC,QAAQ,EAAC;kBAAS;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClK,CAAC,eACNxC,OAAA;kBAAMmC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEkB;gBAAO;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAJxCe,KAAK;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLX,MAAM,CAACmB,MAAM,GAAG,CAAC,iBAChBhD,OAAA;YAAKmC,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDpC,OAAA;cAAImC,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjExC,OAAA;cAAKmC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EACpCP,MAAM,CAAC2B,KAAK,CAAC,CAAC,CAAC,CAACH,GAAG,CAAC,CAACI,KAAK,EAAEF,KAAK,kBAChCvD,OAAA;gBAEEiD,GAAG,EAAEQ,KAAM;gBACXP,GAAG,EAAE,GAAG7C,QAAQ,CAACoB,IAAI,IAAI8B,KAAK,GAAG,CAAC,EAAG;gBACrCpB,SAAS,EAAC;cAAkC,GAHvCoB,KAAK;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIX,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNxC,OAAA;UAAKmC,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BpC,OAAA;YAAKmC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDpC,OAAA;cAAImC,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC1EjC,OAAO,CAACyC,MAAM,KAAK,CAAC,gBACnBhD,OAAA;cAAGmC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAsC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,gBAEvExC,OAAA;cAAKmC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACnD7B,OAAO,CAAC8C,GAAG,CAACK,MAAM,iBACjB1D,OAAA,CAACJ,UAAU;gBAAiB8D,MAAM,EAAEA;cAAO,GAA1BA,MAAM,CAACvD,EAAE;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAmB,CAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtC,EAAA,CAhPID,eAAe;EAAA,QACJT,SAAS,EACIM,OAAO;AAAA;AAAA6D,EAAA,GAF/B1D,eAAe;AAkPrB,eAAeA,eAAe;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}