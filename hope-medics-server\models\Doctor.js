const db = require('./db');

class Doctor {
  static async create(doctorData) {
    const { name, specialty, hospitalId, userId } = doctorData;
    
    try {
      const query = `
        INSERT INTO Doctors (name, specialty, hospitalId, userId) 
        VALUES (?, ?, ?, ?)
      `;
      const [result] = await db.promise().execute(query, [name, specialty, hospitalId, userId]);
      
      return {
        id: result.insertId,
        name,
        specialty,
        hospitalId,
        userId
      };
    } catch (error) {
      throw error;
    }
  }

  static async findAll() {
    try {
      const query = `
        SELECT d.*, h.name as hospitalName 
        FROM Doctors d 
        LEFT JOIN Hospitals h ON d.hospitalId = h.id 
        ORDER BY d.name
      `;
      const [rows] = await db.promise().execute(query);
      return rows;
    } catch (error) {
      throw error;
    }
  }

  static async findById(id) {
    try {
      const query = `
        SELECT d.*, h.name as hospitalName 
        FROM Doctors d 
        LEFT JOIN Hospitals h ON d.hospitalId = h.id 
        WHERE d.id = ?
      `;
      const [rows] = await db.promise().execute(query, [id]);
      return rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  static async findByHospital(hospitalId) {
    try {
      const query = 'SELECT * FROM Doctors WHERE hospitalId = ? ORDER BY name';
      const [rows] = await db.promise().execute(query, [hospitalId]);
      return rows;
    } catch (error) {
      throw error;
    }
  }

  static async findByUserId(userId) {
    try {
      const query = `
        SELECT d.*, h.name as hospitalName 
        FROM Doctors d 
        LEFT JOIN Hospitals h ON d.hospitalId = h.id 
        WHERE d.userId = ?
      `;
      const [rows] = await db.promise().execute(query, [userId]);
      return rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  static async update(id, doctorData) {
    try {
      const { name, specialty, hospitalId, userId } = doctorData;
      const query = `
        UPDATE Doctors 
        SET name = ?, specialty = ?, hospitalId = ?, userId = ?
        WHERE id = ?
      `;
      await db.promise().execute(query, [name, specialty, hospitalId, userId, id]);
      
      return this.findById(id);
    } catch (error) {
      throw error;
    }
  }

  static async delete(id) {
    try {
      const query = 'DELETE FROM Doctors WHERE id = ?';
      const [result] = await db.promise().execute(query, [id]);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = Doctor;
