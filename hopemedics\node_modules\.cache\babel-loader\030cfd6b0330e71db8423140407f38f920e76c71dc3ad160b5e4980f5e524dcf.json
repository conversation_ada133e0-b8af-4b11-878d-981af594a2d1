{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\hopeMedics\\\\hopemedics\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './context/AuthContext';\n\n// Import pages\nimport Home from './pages/Home';\nimport HospitalDetails from './pages/HospitalDetails';\nimport BookAppointment from './pages/BookAppointment';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport AdminDashboard from './pages/AdminDashboard';\nimport DoctorDashboard from './pages/DoctorDashboard';\nimport ReceptionDashboard from './pages/ReceptionDashboard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/hospital/:id\",\n            element: /*#__PURE__*/_jsxDEV(HospitalDetails, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 50\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/book\",\n            element: /*#__PURE__*/_jsxDEV(BookAppointment, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 42\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/register\",\n            element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin\",\n            element: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/doctor\",\n            element: /*#__PURE__*/_jsxDEV(DoctorDashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 44\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/reception\",\n            element: /*#__PURE__*/_jsxDEV(ReceptionDashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "<PERSON>th<PERSON><PERSON><PERSON>", "Home", "HospitalDetails", "BookAppointment", "<PERSON><PERSON>", "Register", "AdminDashboard", "DoctorDashboard", "ReceptionDashboard", "jsxDEV", "_jsxDEV", "App", "children", "className", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/hopeMedics/hopemedics/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './context/AuthContext';\n\n// Import pages\nimport Home from './pages/Home';\nimport HospitalDetails from './pages/HospitalDetails';\nimport BookAppointment from './pages/BookAppointment';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport AdminDashboard from './pages/AdminDashboard';\nimport DoctorDashboard from './pages/DoctorDashboard';\nimport ReceptionDashboard from './pages/ReceptionDashboard';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <BrowserRouter>\n        <div className=\"App\">\n          <Routes>\n            <Route path=\"/\" element={<Home />} />\n            <Route path=\"/hospital/:id\" element={<HospitalDetails />} />\n            <Route path=\"/book\" element={<BookAppointment />} />\n            <Route path=\"/login\" element={<Login />} />\n            <Route path=\"/register\" element={<Register />} />\n            <Route path=\"/admin\" element={<AdminDashboard />} />\n            <Route path=\"/doctor\" element={<DoctorDashboard />} />\n            <Route path=\"/reception\" element={<ReceptionDashboard />} />\n          </Routes>\n        </div>\n      </BrowserRouter>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAC/D,SAASC,YAAY,QAAQ,uBAAuB;;AAEpD;AACA,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,kBAAkB,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACV,YAAY;IAAAY,QAAA,eACXF,OAAA,CAACb,aAAa;MAAAe,QAAA,eACZF,OAAA;QAAKG,SAAS,EAAC,KAAK;QAAAD,QAAA,eAClBF,OAAA,CAACZ,MAAM;UAAAc,QAAA,gBACLF,OAAA,CAACX,KAAK;YAACe,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEL,OAAA,CAACT,IAAI;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrCT,OAAA,CAACX,KAAK;YAACe,IAAI,EAAC,eAAe;YAACC,OAAO,eAAEL,OAAA,CAACR,eAAe;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DT,OAAA,CAACX,KAAK;YAACe,IAAI,EAAC,OAAO;YAACC,OAAO,eAAEL,OAAA,CAACP,eAAe;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDT,OAAA,CAACX,KAAK;YAACe,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEL,OAAA,CAACN,KAAK;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3CT,OAAA,CAACX,KAAK;YAACe,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEL,OAAA,CAACL,QAAQ;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDT,OAAA,CAACX,KAAK;YAACe,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEL,OAAA,CAACJ,cAAc;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDT,OAAA,CAACX,KAAK;YAACe,IAAI,EAAC,SAAS;YAACC,OAAO,eAAEL,OAAA,CAACH,eAAe;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtDT,OAAA,CAACX,KAAK;YAACe,IAAI,EAAC,YAAY;YAACC,OAAO,eAAEL,OAAA,CAACF,kBAAkB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEnB;AAACC,EAAA,GAnBQT,GAAG;AAqBZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}