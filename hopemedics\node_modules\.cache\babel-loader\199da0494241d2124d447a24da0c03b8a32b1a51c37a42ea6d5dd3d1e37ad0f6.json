{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\hopeMedics\\\\hopemedics\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Check if user is logged in on app start\n    const savedUser = localStorage.getItem('user');\n    if (savedUser) {\n      setUser(JSON.parse(savedUser));\n    }\n    setLoading(false);\n  }, []);\n  const login = userData => {\n    setUser(userData);\n    localStorage.setItem('user', JSON.stringify(userData));\n  };\n  const logout = () => {\n    setUser(null);\n    localStorage.removeItem('user');\n  };\n  const value = {\n    user,\n    login,\n    logout,\n    loading,\n    isAuthenticated: !!user,\n    isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin',\n    isDoctor: (user === null || user === void 0 ? void 0 : user.role) === 'doctor',\n    isReceptionist: (user === null || user === void 0 ? void 0 : user.role) === 'receptionist',\n    isPatient: (user === null || user === void 0 ? void 0 : user.role) === 'patient'\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "savedUser", "localStorage", "getItem", "JSON", "parse", "login", "userData", "setItem", "stringify", "logout", "removeItem", "value", "isAuthenticated", "isAdmin", "role", "isDoctor", "isReceptionist", "isPatient", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/hopeMedics/hopemedics/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Check if user is logged in on app start\n    const savedUser = localStorage.getItem('user');\n    if (savedUser) {\n      setUser(JSON.parse(savedUser));\n    }\n    setLoading(false);\n  }, []);\n\n  const login = (userData) => {\n    setUser(userData);\n    localStorage.setItem('user', JSON.stringify(userData));\n  };\n\n  const logout = () => {\n    setUser(null);\n    localStorage.removeItem('user');\n  };\n\n  const value = {\n    user,\n    login,\n    logout,\n    loading,\n    isAuthenticated: !!user,\n    isAdmin: user?.role === 'admin',\n    isDoctor: user?.role === 'doctor',\n    isReceptionist: user?.role === 'receptionist',\n    isPatient: user?.role === 'patient'\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,WAAW,gBAAGN,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGR,UAAU,CAACK,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACA,MAAMe,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC9C,IAAIF,SAAS,EAAE;MACbH,OAAO,CAACM,IAAI,CAACC,KAAK,CAACJ,SAAS,CAAC,CAAC;IAChC;IACAD,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,KAAK,GAAIC,QAAQ,IAAK;IAC1BT,OAAO,CAACS,QAAQ,CAAC;IACjBL,YAAY,CAACM,OAAO,CAAC,MAAM,EAAEJ,IAAI,CAACK,SAAS,CAACF,QAAQ,CAAC,CAAC;EACxD,CAAC;EAED,MAAMG,MAAM,GAAGA,CAAA,KAAM;IACnBZ,OAAO,CAAC,IAAI,CAAC;IACbI,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;EACjC,CAAC;EAED,MAAMC,KAAK,GAAG;IACZf,IAAI;IACJS,KAAK;IACLI,MAAM;IACNX,OAAO;IACPc,eAAe,EAAE,CAAC,CAAChB,IAAI;IACvBiB,OAAO,EAAE,CAAAjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,IAAI,MAAK,OAAO;IAC/BC,QAAQ,EAAE,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,IAAI,MAAK,QAAQ;IACjCE,cAAc,EAAE,CAAApB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,IAAI,MAAK,cAAc;IAC7CG,SAAS,EAAE,CAAArB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,IAAI,MAAK;EAC5B,CAAC;EAED,oBACE3B,OAAA,CAACC,WAAW,CAAC8B,QAAQ;IAACP,KAAK,EAAEA,KAAM;IAAAjB,QAAA,EAChCA;EAAQ;IAAAyB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC3B,GAAA,CAxCWF,YAAY;AAAA8B,EAAA,GAAZ9B,YAAY;AAAA,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}