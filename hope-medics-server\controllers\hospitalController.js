const Hospital = require('../models/Hospital');

const hospitalController = {
  async getAllHospitals(req, res) {
    try {
      const hospitals = await Hospital.findAll();
      
      res.json({
        success: true,
        data: hospitals
      });
    } catch (error) {
      console.error('Get hospitals error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  async getHospitalById(req, res) {
    try {
      const { id } = req.params;
      const hospital = await Hospital.findById(id);
      
      if (!hospital) {
        return res.status(404).json({
          success: false,
          message: 'Hospital not found'
        });
      }

      res.json({
        success: true,
        data: hospital
      });
    } catch (error) {
      console.error('Get hospital error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  async createHospital(req, res) {
    try {
      const { name, address, contact, bedPricePerDay, photos, services } = req.body;

      // Validation
      if (!name || !address || !contact || !bedPricePerDay) {
        return res.status(400).json({
          success: false,
          message: 'Name, address, contact, and bed price are required'
        });
      }

      const hospital = await Hospital.create({
        name,
        address,
        contact,
        bedPricePerDay,
        photos,
        services
      });

      res.status(201).json({
        success: true,
        message: 'Hospital created successfully',
        data: hospital
      });
    } catch (error) {
      console.error('Create hospital error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  async updateHospital(req, res) {
    try {
      const { id } = req.params;
      const { name, address, contact, bedPricePerDay, photos, services } = req.body;

      const hospital = await Hospital.findById(id);
      if (!hospital) {
        return res.status(404).json({
          success: false,
          message: 'Hospital not found'
        });
      }

      const updatedHospital = await Hospital.update(id, {
        name,
        address,
        contact,
        bedPricePerDay,
        photos,
        services
      });

      res.json({
        success: true,
        message: 'Hospital updated successfully',
        data: updatedHospital
      });
    } catch (error) {
      console.error('Update hospital error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  async deleteHospital(req, res) {
    try {
      const { id } = req.params;

      const hospital = await Hospital.findById(id);
      if (!hospital) {
        return res.status(404).json({
          success: false,
          message: 'Hospital not found'
        });
      }

      const deleted = await Hospital.delete(id);
      if (!deleted) {
        return res.status(500).json({
          success: false,
          message: 'Failed to delete hospital'
        });
      }

      res.json({
        success: true,
        message: 'Hospital deleted successfully'
      });
    } catch (error) {
      console.error('Delete hospital error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
};

module.exports = hospitalController;
