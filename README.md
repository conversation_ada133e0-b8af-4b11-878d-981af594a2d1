# Hope Medics - Hospital Management System

A full-stack web application for hospital management that allows users to browse hospitals, view pricing, book doctor appointments, and manage hospital-related operations with role-based access control.

## 🏥 Features

### For Patients
- Browse hospitals with photos, pricing, and services
- View detailed hospital information and available doctors
- Book appointments with preferred doctors
- View appointment history
- User registration and authentication

### For Doctors
- View scheduled appointments
- Update appointment status (completed/cancelled)
- Manage patient information
- Dashboard with appointment statistics

### For Receptionists
- Manual appointment booking for walk-in patients
- View today's appointments
- Patient check-in management
- Quick booking interface

### For Administrators
- Complete system overview dashboard
- Manage hospitals, doctors, and users
- View all appointments and statistics
- System administration tools

## 🛠 Tech Stack

### Frontend
- **React.js** - User interface library
- **React Router DOM** - Client-side routing
- **Tailwind CSS** - Utility-first CSS framework
- **Axios** - HTTP client for API requests
- **Context API** - State management for authentication

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web application framework
- **MySQL** - Relational database
- **JWT** - JSON Web Tokens for authentication
- **bcryptjs** - Password hashing
- **CORS** - Cross-origin resource sharing

## 📁 Project Structure

```
hopeMedics/
├── hopemedics/                 # Frontend (React)
│   ├── public/
│   ├── src/
│   │   ├── components/         # Reusable UI components
│   │   ├── pages/             # Page components
│   │   ├── context/           # React Context providers
│   │   ├── services/          # API service functions
│   │   └── utils/             # Utility functions
│   ├── package.json
│   └── tailwind.config.js
├── hope-medics-server/        # Backend (Node.js/Express)
│   ├── controllers/           # Business logic
│   ├── models/               # Database models
│   ├── routes/               # API routes
│   ├── middleware/           # Custom middleware
│   ├── init-db.sql          # Database initialization
│   ├── server.js            # Main server file
│   └── package.json
└── README.md
```

## 🚀 Getting Started

### Prerequisites
- Node.js (v14 or higher)
- MySQL (v8 or higher)
- npm or yarn

### Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd hopeMedics
```

2. **Set up the backend:**
```bash
cd hope-medics-server
npm install
```

3. **Configure the database:**
```bash
# Create MySQL database
mysql -u root -p
CREATE DATABASE hopemedics;
exit

# Initialize database with sample data
mysql -u root -p hopemedics < init-db.sql
```

4. **Configure environment variables:**
```bash
# In hope-medics-server directory
cp .env.example .env
# Edit .env with your database credentials
```

5. **Start the backend server:**
```bash
npm run dev
# Server will run on http://localhost:5000
```

6. **Set up the frontend:**
```bash
cd ../hopemedics
npm install
```

7. **Start the frontend:**
```bash
npm start
# Application will open on http://localhost:3000
```

## 🔐 Demo Credentials

Use these credentials to test different user roles:

| Role | Email | Password |
|------|-------|----------|
| Admin | <EMAIL> | password |
| Doctor | <EMAIL> | password |
| Receptionist | <EMAIL> | password |
| Patient | <EMAIL> | password |

## 📱 Application Screenshots

### Home Page
- Hospital listings with search functionality
- Hospital cards showing pricing and services
- Responsive design for all devices

### Hospital Details
- Detailed hospital information
- Photo gallery
- Available doctors list
- Direct appointment booking

### Dashboards
- **Admin Dashboard**: System overview with statistics
- **Doctor Dashboard**: Appointment management
- **Reception Dashboard**: Walk-in patient booking

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile

### Hospitals
- `GET /api/hospitals` - List all hospitals
- `GET /api/hospitals/:id` - Get hospital details
- `POST /api/hospitals` - Create hospital (admin)

### Doctors
- `GET /api/doctors` - List all doctors
- `GET /api/doctors/hospital/:id` - Get doctors by hospital

### Appointments
- `POST /api/appointments` - Book appointment
- `GET /api/appointments/doctor/:id` - Get doctor appointments
- `POST /api/appointments/reception/manual` - Manual booking

## 🎨 Design Features

- **Responsive Design**: Works on desktop, tablet, and mobile
- **Modern UI**: Clean and intuitive interface using Tailwind CSS
- **Role-based Navigation**: Different interfaces for different user types
- **Real-time Updates**: Dynamic content updates
- **Accessibility**: WCAG compliant design elements

## 🔒 Security Features

- JWT-based authentication
- Password hashing with bcryptjs
- Role-based access control
- Protected API routes
- CORS configuration
- Input validation and sanitization

## 🚀 Deployment

### Frontend Deployment
The React app can be deployed to:
- Netlify
- Vercel
- GitHub Pages
- AWS S3 + CloudFront

### Backend Deployment
The Node.js API can be deployed to:
- Heroku
- AWS EC2
- DigitalOcean
- Railway

### Database
- AWS RDS (MySQL)
- Google Cloud SQL
- PlanetScale
- Local MySQL server

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📝 Development Notes

- The application includes mock data for development when the database is not available
- All API endpoints are documented in the backend README
- The frontend uses React Context for state management
- Tailwind CSS is configured with custom colors for the medical theme

## 🐛 Known Issues

- File upload for hospital photos is not implemented (uses placeholder images)
- Email notifications for appointments are not implemented
- Advanced search and filtering features are basic

## 🔮 Future Enhancements

- [ ] Email/SMS notifications
- [ ] File upload for hospital images
- [ ] Advanced search and filtering
- [ ] Payment integration
- [ ] Telemedicine features
- [ ] Mobile app development
- [ ] Multi-language support

## 📄 License

This project is licensed under the ISC License - see the LICENSE file for details.

## 👥 Team

Hope Medics Development Team

## 📞 Support

For support, email <EMAIL> or create an issue in the repository.
