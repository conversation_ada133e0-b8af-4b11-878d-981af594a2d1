# Hope Medics Server

Backend API for the Hope Medics hospital management system.

## Features

- User authentication and authorization (JWT)
- Role-based access control (<PERSON><PERSON>, <PERSON>, Receptionist, Patient)
- Hospital management
- Doctor management
- Appointment booking and management
- RESTful API design
- MySQL database integration

## Tech Stack

- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **MySQL** - Database
- **JWT** - Authentication
- **bcryptjs** - Password hashing
- **CORS** - Cross-origin resource sharing

## Prerequisites

- Node.js (v14 or higher)
- MySQL (v8 or higher)
- npm or yarn

## Installation

1. Clone the repository and navigate to the server directory:
```bash
cd hope-medics-server
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
```
Edit the `.env` file with your database credentials and other settings.

4. Set up the database:
```bash
# Create the database and tables
mysql -u root -p < init-db.sql
```

5. Start the development server:
```bash
npm run dev
```

The server will start on `http://localhost:5000`

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/profile` - Get user profile (protected)
- `PUT /api/auth/profile` - Update user profile (protected)

### Hospitals
- `GET /api/hospitals` - Get all hospitals
- `GET /api/hospitals/:id` - Get hospital by ID
- `POST /api/hospitals` - Create hospital (admin only)
- `PUT /api/hospitals/:id` - Update hospital (admin only)
- `DELETE /api/hospitals/:id` - Delete hospital (admin only)

### Doctors
- `GET /api/doctors` - Get all doctors
- `GET /api/doctors/:id` - Get doctor by ID
- `GET /api/doctors/hospital/:hospitalId` - Get doctors by hospital
- `POST /api/doctors` - Create doctor (admin only)
- `PUT /api/doctors/:id` - Update doctor (admin only)
- `DELETE /api/doctors/:id` - Delete doctor (admin only)

### Appointments
- `GET /api/appointments` - Get all appointments (protected)
- `GET /api/appointments/:id` - Get appointment by ID (protected)
- `GET /api/appointments/patient/:patientId` - Get patient appointments (protected)
- `GET /api/appointments/doctor/:doctorId` - Get doctor appointments (protected)
- `POST /api/appointments` - Book appointment (protected)
- `POST /api/appointments/reception/manual` - Manual booking (receptionist/admin)
- `PUT /api/appointments/:id` - Update appointment (protected)
- `PATCH /api/appointments/:id/status` - Update appointment status (doctor/admin)
- `DELETE /api/appointments/:id` - Cancel appointment (protected)

## User Roles

### Patient
- View hospitals and doctors
- Book appointments
- View own appointments
- Update profile

### Doctor
- View assigned appointments
- Update appointment status
- View patient information

### Receptionist
- Manual appointment booking for walk-in patients
- View today's appointments
- Manage patient check-ins

### Admin
- Full system access
- Manage hospitals and doctors
- View all appointments and users
- System administration

## Demo Credentials

For testing purposes, use these credentials:

- **Admin**: <EMAIL> / password
- **Doctor**: <EMAIL> / password
- **Receptionist**: <EMAIL> / password
- **Patient**: <EMAIL> / password

## Development

### Scripts
- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm test` - Run tests (not implemented yet)

### Database Schema

The application uses the following main tables:
- `Users` - User accounts and authentication
- `Hospitals` - Hospital information
- `Doctors` - Doctor profiles and specialties
- `Appointments` - Appointment bookings and scheduling

## Environment Variables

```env
# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=hopemedics

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h

# Server Configuration
PORT=5000
NODE_ENV=development

# Frontend URL for CORS
FRONTEND_URL=http://localhost:3000
```

## Error Handling

The API uses standard HTTP status codes:
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

All responses follow this format:
```json
{
  "success": true/false,
  "message": "Description",
  "data": {} // Only on success
}
```

## Security

- Passwords are hashed using bcryptjs
- JWT tokens for authentication
- Role-based access control
- CORS protection
- Input validation and sanitization

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the ISC License.
