const Doctor = require('../models/Doctor');

const doctorController = {
  async getAllDoctors(req, res) {
    try {
      const doctors = await Doctor.findAll();
      
      res.json({
        success: true,
        data: doctors
      });
    } catch (error) {
      console.error('Get doctors error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  async getDoctorById(req, res) {
    try {
      const { id } = req.params;
      const doctor = await Doctor.findById(id);
      
      if (!doctor) {
        return res.status(404).json({
          success: false,
          message: 'Doctor not found'
        });
      }

      res.json({
        success: true,
        data: doctor
      });
    } catch (error) {
      console.error('Get doctor error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  async getDoctorsByHospital(req, res) {
    try {
      const { hospitalId } = req.params;
      const doctors = await Doctor.findByHospital(hospitalId);
      
      res.json({
        success: true,
        data: doctors
      });
    } catch (error) {
      console.error('Get doctors by hospital error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  async createDoctor(req, res) {
    try {
      const { name, specialty, hospitalId, userId } = req.body;

      // Validation
      if (!name || !specialty || !hospitalId) {
        return res.status(400).json({
          success: false,
          message: 'Name, specialty, and hospital ID are required'
        });
      }

      const doctor = await Doctor.create({
        name,
        specialty,
        hospitalId,
        userId
      });

      res.status(201).json({
        success: true,
        message: 'Doctor created successfully',
        data: doctor
      });
    } catch (error) {
      console.error('Create doctor error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  async updateDoctor(req, res) {
    try {
      const { id } = req.params;
      const { name, specialty, hospitalId, userId } = req.body;

      const doctor = await Doctor.findById(id);
      if (!doctor) {
        return res.status(404).json({
          success: false,
          message: 'Doctor not found'
        });
      }

      const updatedDoctor = await Doctor.update(id, {
        name,
        specialty,
        hospitalId,
        userId
      });

      res.json({
        success: true,
        message: 'Doctor updated successfully',
        data: updatedDoctor
      });
    } catch (error) {
      console.error('Update doctor error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  async deleteDoctor(req, res) {
    try {
      const { id } = req.params;

      const doctor = await Doctor.findById(id);
      if (!doctor) {
        return res.status(404).json({
          success: false,
          message: 'Doctor not found'
        });
      }

      const deleted = await Doctor.delete(id);
      if (!deleted) {
        return res.status(500).json({
          success: false,
          message: 'Failed to delete doctor'
        });
      }

      res.json({
        success: true,
        message: 'Doctor deleted successfully'
      });
    } catch (error) {
      console.error('Delete doctor error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
};

module.exports = doctorController;
