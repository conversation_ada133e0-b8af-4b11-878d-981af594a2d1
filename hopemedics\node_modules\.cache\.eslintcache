[{"D:\\WebProject\\hopeMedics\\hopemedics\\src\\index.js": "1", "D:\\WebProject\\hopeMedics\\hopemedics\\src\\App.js": "2", "D:\\WebProject\\hopeMedics\\hopemedics\\src\\reportWebVitals.js": "3", "D:\\WebProject\\hopeMedics\\hopemedics\\src\\pages\\Home.js": "4", "D:\\WebProject\\hopeMedics\\hopemedics\\src\\context\\AuthContext.js": "5", "D:\\WebProject\\hopeMedics\\hopemedics\\src\\pages\\BookAppointment.js": "6", "D:\\WebProject\\hopeMedics\\hopemedics\\src\\pages\\Register.js": "7", "D:\\WebProject\\hopeMedics\\hopemedics\\src\\pages\\Login.js": "8", "D:\\WebProject\\hopeMedics\\hopemedics\\src\\pages\\HospitalDetails.js": "9", "D:\\WebProject\\hopeMedics\\hopemedics\\src\\pages\\DoctorDashboard.js": "10", "D:\\WebProject\\hopeMedics\\hopemedics\\src\\pages\\AdminDashboard.js": "11", "D:\\WebProject\\hopeMedics\\hopemedics\\src\\pages\\ReceptionDashboard.js": "12", "D:\\WebProject\\hopeMedics\\hopemedics\\src\\components\\DoctorCard.js": "13", "D:\\WebProject\\hopeMedics\\hopemedics\\src\\components\\HospitalCard.js": "14", "D:\\WebProject\\hopeMedics\\hopemedics\\src\\components\\Navbar.js": "15", "D:\\WebProject\\hopeMedics\\hopemedics\\src\\components\\AppointmentForm.js": "16", "D:\\WebProject\\hopeMedics\\hopemedics\\src\\services\\api.js": "17"}, {"size": 535, "mtime": 1748891383436, "results": "18", "hashOfConfig": "19"}, {"size": 1287, "mtime": 1748892147942, "results": "20", "hashOfConfig": "19"}, {"size": 362, "mtime": 1748891383573, "results": "21", "hashOfConfig": "19"}, {"size": 4484, "mtime": 1748891944437, "results": "22", "hashOfConfig": "19"}, {"size": 1240, "mtime": 1748891866263, "results": "23", "hashOfConfig": "19"}, {"size": 5530, "mtime": 1748892036884, "results": "24", "hashOfConfig": "19"}, {"size": 6967, "mtime": 1748892015867, "results": "25", "hashOfConfig": "19"}, {"size": 6546, "mtime": 1748891996063, "results": "26", "hashOfConfig": "19"}, {"size": 9410, "mtime": 1748891976247, "results": "27", "hashOfConfig": "19"}, {"size": 12857, "mtime": 1748892104517, "results": "28", "hashOfConfig": "19"}, {"size": 10375, "mtime": 1748892068449, "results": "29", "hashOfConfig": "19"}, {"size": 10687, "mtime": 1748892138200, "results": "30", "hashOfConfig": "19"}, {"size": 1573, "mtime": 1748891906263, "results": "31", "hashOfConfig": "19"}, {"size": 2452, "mtime": 1748891897093, "results": "32", "hashOfConfig": "19"}, {"size": 3009, "mtime": 1748891886510, "results": "33", "hashOfConfig": "19"}, {"size": 6208, "mtime": 1748892575726, "results": "34", "hashOfConfig": "19"}, {"size": 1985, "mtime": 1748891875555, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kbv88i", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\WebProject\\hopeMedics\\hopemedics\\src\\index.js", [], [], "D:\\WebProject\\hopeMedics\\hopemedics\\src\\App.js", [], [], "D:\\WebProject\\hopeMedics\\hopemedics\\src\\reportWebVitals.js", [], [], "D:\\WebProject\\hopeMedics\\hopemedics\\src\\pages\\Home.js", ["87"], [], "D:\\WebProject\\hopeMedics\\hopemedics\\src\\context\\AuthContext.js", [], [], "D:\\WebProject\\hopeMedics\\hopemedics\\src\\pages\\BookAppointment.js", ["88"], [], "D:\\WebProject\\hopeMedics\\hopemedics\\src\\pages\\Register.js", [], [], "D:\\WebProject\\hopeMedics\\hopemedics\\src\\pages\\Login.js", [], [], "D:\\WebProject\\hopeMedics\\hopemedics\\src\\pages\\HospitalDetails.js", ["89", "90", "91"], [], "D:\\WebProject\\hopeMedics\\hopemedics\\src\\pages\\DoctorDashboard.js", ["92", "93"], [], "D:\\WebProject\\hopeMedics\\hopemedics\\src\\pages\\AdminDashboard.js", ["94"], [], "D:\\WebProject\\hopeMedics\\hopemedics\\src\\pages\\ReceptionDashboard.js", ["95"], [], "D:\\WebProject\\hopeMedics\\hopemedics\\src\\components\\DoctorCard.js", [], [], "D:\\WebProject\\hopeMedics\\hopemedics\\src\\components\\HospitalCard.js", [], [], "D:\\WebProject\\hopeMedics\\hopemedics\\src\\components\\Navbar.js", [], [], "D:\\WebProject\\hopeMedics\\hopemedics\\src\\components\\AppointmentForm.js", [], [], "D:\\WebProject\\hopeMedics\\hopemedics\\src\\services\\api.js", [], [], {"ruleId": "96", "severity": 1, "message": "97", "line": 14, "column": 6, "nodeType": "98", "endLine": 14, "endColumn": 8, "suggestions": "99"}, {"ruleId": "96", "severity": 1, "message": "100", "line": 27, "column": 6, "nodeType": "98", "endLine": 27, "endColumn": 33, "suggestions": "101"}, {"ruleId": "102", "severity": 1, "message": "103", "line": 14, "column": 10, "nodeType": "104", "messageId": "105", "endLine": 14, "endColumn": 15}, {"ruleId": "102", "severity": 1, "message": "106", "line": 14, "column": 17, "nodeType": "104", "messageId": "105", "endLine": 14, "endColumn": 25}, {"ruleId": "96", "severity": 1, "message": "107", "line": 19, "column": 6, "nodeType": "98", "endLine": 19, "endColumn": 10, "suggestions": "108"}, {"ruleId": "96", "severity": 1, "message": "109", "line": 28, "column": 6, "nodeType": "98", "endLine": 28, "endColumn": 26, "suggestions": "110"}, {"ruleId": "96", "severity": 1, "message": "111", "line": 32, "column": 6, "nodeType": "98", "endLine": 32, "endColumn": 28, "suggestions": "112"}, {"ruleId": "96", "severity": 1, "message": "113", "line": 27, "column": 6, "nodeType": "98", "endLine": 27, "endColumn": 25, "suggestions": "114"}, {"ruleId": "96", "severity": 1, "message": "115", "line": 27, "column": 6, "nodeType": "98", "endLine": 27, "endColumn": 32, "suggestions": "116"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchHospitals'. Either include it or remove the dependency array.", "ArrayExpression", ["117"], "React Hook useEffect has missing dependencies: 'fetchDoctors' and 'fetchHospitals'. Either include them or remove the dependency array.", ["118"], "no-unused-vars", "'error' is assigned a value but never used.", "Identifier", "unusedVar", "'setError' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchDoctors' and 'fetchHospitalDetails'. Either include them or remove the dependency array.", ["119"], "React Hook useEffect has a missing dependency: 'fetchAppointments'. Either include it or remove the dependency array.", ["120"], "React Hook useEffect has a missing dependency: 'filterAppointments'. Either include it or remove the dependency array.", ["121"], "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", ["122"], "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["123"], {"desc": "124", "fix": "125"}, {"desc": "126", "fix": "127"}, {"desc": "128", "fix": "129"}, {"desc": "130", "fix": "131"}, {"desc": "132", "fix": "133"}, {"desc": "134", "fix": "135"}, {"desc": "136", "fix": "137"}, "Update the dependencies array to be: [fetchHospitals]", {"range": "138", "text": "139"}, "Update the dependencies array to be: [fetchDoctors, fetchHospitals, isAuthenticated, navigate]", {"range": "140", "text": "141"}, "Update the dependencies array to be: [fetchDoctors, fetchHospitalDetails, id]", {"range": "142", "text": "143"}, "Update the dependencies array to be: [fetchAppointments, isDoctor, navigate]", {"range": "144", "text": "145"}, "Update the dependencies array to be: [appointments, filter, filterAppointments]", {"range": "146", "text": "147"}, "Update the dependencies array to be: [fetchDashboardData, isAdmin, navigate]", {"range": "148", "text": "149"}, "Update the dependencies array to be: [fetchData, isReceptionist, navigate]", {"range": "150", "text": "151"}, [462, 464], "[fetchHospitals]", [878, 905], "[fetchDoctors, fetchHospitals, isAuthenticated, navigate]", [674, 678], "[fetchDoctors, fetchHospitalDetails, id]", [810, 830], "[fetchAppointments, isDoctor, navigate]", [885, 907], "[appointments, filter, filterAppointments]", [752, 771], "[fetchDashboardData, isAdmin, navigate]", [928, 954], "[fetch<PERSON><PERSON>, isReceptionist, navigate]"]