[{"D:\\WebProject\\hopeMedics\\hopemedics\\src\\index.js": "1", "D:\\WebProject\\hopeMedics\\hopemedics\\src\\App.js": "2", "D:\\WebProject\\hopeMedics\\hopemedics\\src\\reportWebVitals.js": "3"}, {"size": 535, "mtime": 1748891383436, "results": "4", "hashOfConfig": "5"}, {"size": 528, "mtime": 1748891383238, "results": "6", "hashOfConfig": "5"}, {"size": 362, "mtime": 1748891383573, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "suppressedMessages": "10", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kbv88i", {"filePath": "11", "messages": "12", "suppressedMessages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\WebProject\\hopeMedics\\hopemedics\\src\\index.js", [], [], "D:\\WebProject\\hopeMedics\\hopemedics\\src\\App.js", [], [], "D:\\WebProject\\hopeMedics\\hopemedics\\src\\reportWebVitals.js", [], []]