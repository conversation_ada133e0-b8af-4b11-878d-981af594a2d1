const db = require('./db');

class Hospital {
  static async create(hospitalData) {
    const { name, address, contact, bedPricePerDay, photos, services } = hospitalData;
    
    try {
      const query = `
        INSERT INTO Hospitals (name, address, contact, bedPricePerDay, photos, services) 
        VALUES (?, ?, ?, ?, ?, ?)
      `;
      const [result] = await db.promise().execute(query, [
        name, 
        address, 
        contact, 
        bedPricePerDay, 
        JSON.stringify(photos || []), 
        JSON.stringify(services || [])
      ]);
      
      return {
        id: result.insertId,
        name,
        address,
        contact,
        bedPricePerDay,
        photos: JSON.stringify(photos || []),
        services: JSON.stringify(services || [])
      };
    } catch (error) {
      throw error;
    }
  }

  static async findAll() {
    try {
      const query = 'SELECT * FROM Hospitals ORDER BY name';
      const [rows] = await db.promise().execute(query);
      return rows;
    } catch (error) {
      throw error;
    }
  }

  static async findById(id) {
    try {
      const query = 'SELECT * FROM Hospitals WHERE id = ?';
      const [rows] = await db.promise().execute(query, [id]);
      return rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  static async update(id, hospitalData) {
    try {
      const { name, address, contact, bedPricePerDay, photos, services } = hospitalData;
      const query = `
        UPDATE Hospitals 
        SET name = ?, address = ?, contact = ?, bedPricePerDay = ?, photos = ?, services = ?
        WHERE id = ?
      `;
      await db.promise().execute(query, [
        name, 
        address, 
        contact, 
        bedPricePerDay, 
        JSON.stringify(photos || []), 
        JSON.stringify(services || []),
        id
      ]);
      
      return this.findById(id);
    } catch (error) {
      throw error;
    }
  }

  static async delete(id) {
    try {
      const query = 'DELETE FROM Hospitals WHERE id = ?';
      const [result] = await db.promise().execute(query, [id]);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = Hospital;
