const express = require('express');
const router = express.Router();
const hospitalController = require('../controllers/hospitalController');
const { authenticate, requireAdmin, optionalAuth } = require('../middleware/auth');

// Public routes
router.get('/', optionalAuth, hospitalController.getAllHospitals);
router.get('/:id', optionalAuth, hospitalController.getHospitalById);

// Admin only routes
router.post('/', authenticate, requireAdmin, hospitalController.createHospital);
router.put('/:id', authenticate, requireAdmin, hospitalController.updateHospital);
router.delete('/:id', authenticate, requireAdmin, hospitalController.deleteHospital);

module.exports = router;
