const mysql = require('mysql2');
require('dotenv').config();

const connection = mysql.createConnection({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'hopemedics'
});

connection.connect((err) => {
  if (err) {
    console.error('Error connecting to MySQL database:', err);
    console.log('Note: Make sure MySQL is running and the database exists');
    console.log('For development, the app will work with mock data if database is not available');
  } else {
    console.log('Connected to MySQL database');
  }
});

// Create tables if they don't exist
const createTables = () => {
  const createUsersTable = `
    CREATE TABLE IF NOT EXISTS Users (
      id INT AUTO_INCREMENT PRIMARY KEY,
      name VARCHA<PERSON>(100) NOT NULL,
      email VARCHAR(100) UNIQUE NOT NULL,
      password VARCHAR(255) NOT NULL,
      role ENUM('patient', 'doctor', 'admin', 'receptionist') DEFAULT 'patient',
      createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `;

  const createHospitalsTable = `
    CREATE TABLE IF NOT EXISTS Hospitals (
      id INT AUTO_INCREMENT PRIMARY KEY,
      name VARCHAR(100) NOT NULL,
      address TEXT NOT NULL,
      contact VARCHAR(50) NOT NULL,
      bedPricePerDay DECIMAL(10,2) NOT NULL,
      photos TEXT,
      services TEXT,
      createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `;

  const createDoctorsTable = `
    CREATE TABLE IF NOT EXISTS Doctors (
      id INT AUTO_INCREMENT PRIMARY KEY,
      name VARCHAR(100) NOT NULL,
      specialty VARCHAR(100) NOT NULL,
      hospitalId INT,
      userId INT,
      createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (hospitalId) REFERENCES Hospitals(id) ON DELETE SET NULL,
      FOREIGN KEY (userId) REFERENCES Users(id) ON DELETE SET NULL
    )
  `;

  const createAppointmentsTable = `
    CREATE TABLE IF NOT EXISTS Appointments (
      id INT AUTO_INCREMENT PRIMARY KEY,
      patientId INT,
      doctorId INT,
      hospitalId INT,
      date DATE NOT NULL,
      time VARCHAR(10) NOT NULL,
      status VARCHAR(20) DEFAULT 'Scheduled',
      notes TEXT,
      patientName VARCHAR(100),
      patientEmail VARCHAR(100),
      patientPhone VARCHAR(20),
      createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (patientId) REFERENCES Users(id) ON DELETE CASCADE,
      FOREIGN KEY (doctorId) REFERENCES Doctors(id) ON DELETE CASCADE,
      FOREIGN KEY (hospitalId) REFERENCES Hospitals(id) ON DELETE CASCADE
    )
  `;

  connection.query(createUsersTable, (err) => {
    if (err) console.error('Error creating Users table:', err);
  });

  connection.query(createHospitalsTable, (err) => {
    if (err) console.error('Error creating Hospitals table:', err);
  });

  connection.query(createDoctorsTable, (err) => {
    if (err) console.error('Error creating Doctors table:', err);
  });

  connection.query(createAppointmentsTable, (err) => {
    if (err) console.error('Error creating Appointments table:', err);
  });
};

// Initialize tables
createTables();

module.exports = connection;
