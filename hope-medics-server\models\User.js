const db = require('./db');
const bcrypt = require('bcryptjs');

class User {
  static async create(userData) {
    const { name, email, password, role = 'patient' } = userData;
    
    try {
      // Hash password
      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash(password, saltRounds);
      
      const query = 'INSERT INTO Users (name, email, password, role) VALUES (?, ?, ?, ?)';
      const [result] = await db.promise().execute(query, [name, email, hashedPassword, role]);
      
      return {
        id: result.insertId,
        name,
        email,
        role
      };
    } catch (error) {
      throw error;
    }
  }

  static async findByEmail(email) {
    try {
      const query = 'SELECT * FROM Users WHERE email = ?';
      const [rows] = await db.promise().execute(query, [email]);
      return rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  static async findById(id) {
    try {
      const query = 'SELECT id, name, email, role, createdAt FROM Users WHERE id = ?';
      const [rows] = await db.promise().execute(query, [id]);
      return rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  static async findAll() {
    try {
      const query = 'SELECT id, name, email, role, createdAt FROM Users ORDER BY createdAt DESC';
      const [rows] = await db.promise().execute(query);
      return rows;
    } catch (error) {
      throw error;
    }
  }

  static async update(id, userData) {
    try {
      const { name, email, role } = userData;
      const query = 'UPDATE Users SET name = ?, email = ?, role = ? WHERE id = ?';
      await db.promise().execute(query, [name, email, role, id]);
      
      return this.findById(id);
    } catch (error) {
      throw error;
    }
  }

  static async delete(id) {
    try {
      const query = 'DELETE FROM Users WHERE id = ?';
      const [result] = await db.promise().execute(query, [id]);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  static async validatePassword(plainPassword, hashedPassword) {
    return await bcrypt.compare(plainPassword, hashedPassword);
  }
}

module.exports = User;
