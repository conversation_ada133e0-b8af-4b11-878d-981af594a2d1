{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\hopeMedics\\\\hopemedics\\\\src\\\\pages\\\\ReceptionDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { hospitalsAPI, doctorsAPI, appointmentsAPI } from '../services/api';\nimport AppointmentForm from '../components/AppointmentForm';\nimport Navbar from '../components/Navbar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ReceptionDashboard = () => {\n  _s();\n  const {\n    user,\n    isReceptionist\n  } = useAuth();\n  const navigate = useNavigate();\n  const [hospitals, setHospitals] = useState([]);\n  const [doctors, setDoctors] = useState([]);\n  const [todayAppointments, setTodayAppointments] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [bookingLoading, setBookingLoading] = useState(false);\n  const [success, setSuccess] = useState('');\n  const [error, setError] = useState('');\n  useEffect(() => {\n    if (!isReceptionist) {\n      navigate('/');\n      return;\n    }\n    fetchData();\n  }, [isReceptionist, navigate]);\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      const [hospitalsRes, doctorsRes] = await Promise.all([hospitalsAPI.getAll(), doctorsAPI.getAll()]);\n      setHospitals(hospitalsRes.data);\n      setDoctors(doctorsRes.data);\n      await fetchTodayAppointments();\n    } catch (err) {\n      console.error('Error fetching data:', err);\n\n      // Mock data for development\n      setHospitals(getMockHospitals());\n      setDoctors(getMockDoctors());\n      setTodayAppointments(getMockTodayAppointments());\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchTodayAppointments = async () => {\n    try {\n      const response = await appointmentsAPI.getAll();\n      const today = new Date().toISOString().split('T')[0];\n      const todayAppts = response.data.filter(apt => apt.date === today);\n      setTodayAppointments(todayAppts);\n    } catch (err) {\n      console.error('Error fetching today appointments:', err);\n      setTodayAppointments(getMockTodayAppointments());\n    }\n  };\n  const getMockHospitals = () => [{\n    id: 1,\n    name: \"City General Hospital\"\n  }, {\n    id: 2,\n    name: \"Hope Medical Center\"\n  }, {\n    id: 3,\n    name: \"Sunrise Healthcare\"\n  }];\n  const getMockDoctors = () => [{\n    id: 1,\n    name: \"Sarah Johnson\",\n    specialty: \"Cardiology\",\n    hospitalId: 1\n  }, {\n    id: 2,\n    name: \"Michael Chen\",\n    specialty: \"Emergency Medicine\",\n    hospitalId: 1\n  }, {\n    id: 3,\n    name: \"Emily Davis\",\n    specialty: \"Pediatrics\",\n    hospitalId: 1\n  }, {\n    id: 4,\n    name: \"Robert Wilson\",\n    specialty: \"Oncology\",\n    hospitalId: 2\n  }, {\n    id: 5,\n    name: \"Lisa Anderson\",\n    specialty: \"Neurology\",\n    hospitalId: 2\n  }, {\n    id: 6,\n    name: \"David Brown\",\n    specialty: \"Orthopedics\",\n    hospitalId: 2\n  }, {\n    id: 7,\n    name: \"Jennifer Taylor\",\n    specialty: \"Obstetrics & Gynecology\",\n    hospitalId: 3\n  }, {\n    id: 8,\n    name: \"Mark Thompson\",\n    specialty: \"Dermatology\",\n    hospitalId: 3\n  }, {\n    id: 9,\n    name: \"Amanda White\",\n    specialty: \"Psychiatry\",\n    hospitalId: 3\n  }];\n  const getMockTodayAppointments = () => [{\n    id: 1,\n    patientName: 'John Doe',\n    doctorName: 'Dr. Sarah Johnson',\n    hospitalName: 'City General Hospital',\n    time: '10:00',\n    status: 'Scheduled'\n  }, {\n    id: 2,\n    patientName: 'Jane Smith',\n    doctorName: 'Dr. Michael Chen',\n    hospitalName: 'City General Hospital',\n    time: '14:30',\n    status: 'Completed'\n  }];\n  const handleManualBooking = async formData => {\n    setBookingLoading(true);\n    setError('');\n    setSuccess('');\n    try {\n      const appointmentData = {\n        ...formData,\n        status: 'Scheduled',\n        bookedBy: 'reception'\n      };\n      await appointmentsAPI.manualBooking(appointmentData);\n      setSuccess('Appointment booked successfully!');\n\n      // Refresh today's appointments\n      await fetchTodayAppointments();\n\n      // Clear success message after 3 seconds\n      setTimeout(() => setSuccess(''), 3000);\n    } catch (err) {\n      console.error('Error booking appointment:', err);\n\n      // For development, simulate success\n      setSuccess('Appointment booked successfully! (Demo mode)');\n      setTimeout(() => setSuccess(''), 3000);\n    } finally {\n      setBookingLoading(false);\n    }\n  };\n  if (!isReceptionist) {\n    return null; // Will redirect\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900\",\n          children: \"Reception Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-6\",\n            children: \"Book Walk-in Appointment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\",\n            children: success\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 15\n          }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center items-center h-32\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(AppointmentForm, {\n            hospitals: hospitals,\n            doctors: doctors,\n            onSubmit: handleManualBooking,\n            loading: bookingLoading,\n            isReceptionMode: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-6\",\n            children: \"Today's Appointments\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), todayAppointments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: \"No appointments scheduled for today.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: todayAppointments.map(appointment => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border border-gray-200 rounded-lg p-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-semibold text-gray-900\",\n                    children: appointment.patientName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: appointment.doctorName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: appointment.hospitalName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: [\"Time: \", appointment.time]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${appointment.status === 'Scheduled' ? 'bg-yellow-100 text-yellow-800' : appointment.status === 'Completed' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                  children: appointment.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 21\n              }, this)\n            }, appointment.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 grid grid-cols-1 md:grid-cols-3 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-white\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"Today's Total\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: todayAppointments.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-white\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"Completed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: todayAppointments.filter(apt => apt.status === 'Completed').length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-white\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"Pending\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: todayAppointments.filter(apt => apt.status === 'Scheduled').length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 5\n  }, this);\n};\n_s(ReceptionDashboard, \"xLoLTbyUYCxc6gEB2JvUZSFf8wI=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = ReceptionDashboard;\nexport default ReceptionDashboard;\nvar _c;\n$RefreshReg$(_c, \"ReceptionDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useAuth", "hospitalsAPI", "doctorsAPI", "appointmentsAPI", "AppointmentForm", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ReceptionDashboard", "_s", "user", "isReceptionist", "navigate", "hospitals", "setHospitals", "doctors", "setDoctors", "todayAppointments", "setTodayAppointments", "loading", "setLoading", "bookingLoading", "setBookingLoading", "success", "setSuccess", "error", "setError", "fetchData", "hospitalsRes", "doctorsRes", "Promise", "all", "getAll", "data", "fetchTodayAppointments", "err", "console", "getMockHospitals", "getMockDoctors", "getMockTodayAppointments", "response", "today", "Date", "toISOString", "split", "todayAppts", "filter", "apt", "date", "id", "name", "specialty", "hospitalId", "patientName", "<PERSON><PERSON><PERSON>", "hospitalName", "time", "status", "handleManualBooking", "formData", "appointmentData", "bookedBy", "manualBooking", "setTimeout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "isReceptionMode", "length", "map", "appointment", "fill", "viewBox", "fillRule", "d", "clipRule", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/hopeMedics/hopemedics/src/pages/ReceptionDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { hospitalsAPI, doctorsAPI, appointmentsAPI } from '../services/api';\nimport AppointmentForm from '../components/AppointmentForm';\nimport Navbar from '../components/Navbar';\n\nconst ReceptionDashboard = () => {\n  const { user, isReceptionist } = useAuth();\n  const navigate = useNavigate();\n  \n  const [hospitals, setHospitals] = useState([]);\n  const [doctors, setDoctors] = useState([]);\n  const [todayAppointments, setTodayAppointments] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [bookingLoading, setBookingLoading] = useState(false);\n  const [success, setSuccess] = useState('');\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    if (!isReceptionist) {\n      navigate('/');\n      return;\n    }\n    \n    fetchData();\n  }, [isReceptionist, navigate]);\n\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      \n      const [hospitalsRes, doctorsRes] = await Promise.all([\n        hospitalsAPI.getAll(),\n        doctorsAPI.getAll()\n      ]);\n\n      setHospitals(hospitalsRes.data);\n      setDoctors(doctorsRes.data);\n      \n      await fetchTodayAppointments();\n      \n    } catch (err) {\n      console.error('Error fetching data:', err);\n      \n      // Mock data for development\n      setHospitals(getMockHospitals());\n      setDoctors(getMockDoctors());\n      setTodayAppointments(getMockTodayAppointments());\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchTodayAppointments = async () => {\n    try {\n      const response = await appointmentsAPI.getAll();\n      const today = new Date().toISOString().split('T')[0];\n      const todayAppts = response.data.filter(apt => apt.date === today);\n      setTodayAppointments(todayAppts);\n    } catch (err) {\n      console.error('Error fetching today appointments:', err);\n      setTodayAppointments(getMockTodayAppointments());\n    }\n  };\n\n  const getMockHospitals = () => [\n    { id: 1, name: \"City General Hospital\" },\n    { id: 2, name: \"Hope Medical Center\" },\n    { id: 3, name: \"Sunrise Healthcare\" }\n  ];\n\n  const getMockDoctors = () => [\n    { id: 1, name: \"Sarah Johnson\", specialty: \"Cardiology\", hospitalId: 1 },\n    { id: 2, name: \"Michael Chen\", specialty: \"Emergency Medicine\", hospitalId: 1 },\n    { id: 3, name: \"Emily Davis\", specialty: \"Pediatrics\", hospitalId: 1 },\n    { id: 4, name: \"Robert Wilson\", specialty: \"Oncology\", hospitalId: 2 },\n    { id: 5, name: \"Lisa Anderson\", specialty: \"Neurology\", hospitalId: 2 },\n    { id: 6, name: \"David Brown\", specialty: \"Orthopedics\", hospitalId: 2 },\n    { id: 7, name: \"Jennifer Taylor\", specialty: \"Obstetrics & Gynecology\", hospitalId: 3 },\n    { id: 8, name: \"Mark Thompson\", specialty: \"Dermatology\", hospitalId: 3 },\n    { id: 9, name: \"Amanda White\", specialty: \"Psychiatry\", hospitalId: 3 }\n  ];\n\n  const getMockTodayAppointments = () => [\n    {\n      id: 1,\n      patientName: 'John Doe',\n      doctorName: 'Dr. Sarah Johnson',\n      hospitalName: 'City General Hospital',\n      time: '10:00',\n      status: 'Scheduled'\n    },\n    {\n      id: 2,\n      patientName: 'Jane Smith',\n      doctorName: 'Dr. Michael Chen',\n      hospitalName: 'City General Hospital',\n      time: '14:30',\n      status: 'Completed'\n    }\n  ];\n\n  const handleManualBooking = async (formData) => {\n    setBookingLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      const appointmentData = {\n        ...formData,\n        status: 'Scheduled',\n        bookedBy: 'reception'\n      };\n\n      await appointmentsAPI.manualBooking(appointmentData);\n      \n      setSuccess('Appointment booked successfully!');\n      \n      // Refresh today's appointments\n      await fetchTodayAppointments();\n      \n      // Clear success message after 3 seconds\n      setTimeout(() => setSuccess(''), 3000);\n      \n    } catch (err) {\n      console.error('Error booking appointment:', err);\n      \n      // For development, simulate success\n      setSuccess('Appointment booked successfully! (Demo mode)');\n      setTimeout(() => setSuccess(''), 3000);\n    } finally {\n      setBookingLoading(false);\n    }\n  };\n\n  if (!isReceptionist) {\n    return null; // Will redirect\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n      \n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">Reception Dashboard</h1>\n          <p className=\"text-gray-600\">Welcome back, {user?.name}</p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Manual Booking Form */}\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-6\">Book Walk-in Appointment</h2>\n            \n            {success && (\n              <div className=\"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\">\n                {success}\n              </div>\n            )}\n            \n            {error && (\n              <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n                {error}\n              </div>\n            )}\n\n            {loading ? (\n              <div className=\"flex justify-center items-center h-32\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n              </div>\n            ) : (\n              <AppointmentForm\n                hospitals={hospitals}\n                doctors={doctors}\n                onSubmit={handleManualBooking}\n                loading={bookingLoading}\n                isReceptionMode={true}\n              />\n            )}\n          </div>\n\n          {/* Today's Appointments */}\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-6\">Today's Appointments</h2>\n            \n            {todayAppointments.length === 0 ? (\n              <div className=\"text-center py-8 text-gray-500\">\n                No appointments scheduled for today.\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                {todayAppointments.map((appointment) => (\n                  <div key={appointment.id} className=\"border border-gray-200 rounded-lg p-4\">\n                    <div className=\"flex justify-between items-start\">\n                      <div>\n                        <h3 className=\"font-semibold text-gray-900\">{appointment.patientName}</h3>\n                        <p className=\"text-sm text-gray-600\">{appointment.doctorName}</p>\n                        <p className=\"text-sm text-gray-600\">{appointment.hospitalName}</p>\n                        <p className=\"text-sm text-gray-500\">Time: {appointment.time}</p>\n                      </div>\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                        appointment.status === 'Scheduled' \n                          ? 'bg-yellow-100 text-yellow-800'\n                          : appointment.status === 'Completed'\n                          ? 'bg-green-100 text-green-800'\n                          : 'bg-red-100 text-red-800'\n                      }`}>\n                        {appointment.status}\n                      </span>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Quick Stats */}\n        <div className=\"mt-8 grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\" clipRule=\"evenodd\"/>\n                  </svg>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Today's Total</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{todayAppointments.length}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"/>\n                  </svg>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Completed</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">\n                    {todayAppointments.filter(apt => apt.status === 'Completed').length}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z\" clipRule=\"evenodd\"/>\n                  </svg>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Pending</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">\n                    {todayAppointments.filter(apt => apt.status === 'Scheduled').length}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ReceptionDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,YAAY,EAAEC,UAAU,EAAEC,eAAe,QAAQ,iBAAiB;AAC3E,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC,IAAI;IAAEC;EAAe,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC1C,MAAMY,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACoB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwB,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACd,IAAI,CAACa,cAAc,EAAE;MACnBC,QAAQ,CAAC,GAAG,CAAC;MACb;IACF;IAEAe,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAChB,cAAc,EAAEC,QAAQ,CAAC,CAAC;EAE9B,MAAMe,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAM,CAACQ,YAAY,EAAEC,UAAU,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACnD9B,YAAY,CAAC+B,MAAM,CAAC,CAAC,EACrB9B,UAAU,CAAC8B,MAAM,CAAC,CAAC,CACpB,CAAC;MAEFlB,YAAY,CAACc,YAAY,CAACK,IAAI,CAAC;MAC/BjB,UAAU,CAACa,UAAU,CAACI,IAAI,CAAC;MAE3B,MAAMC,sBAAsB,CAAC,CAAC;IAEhC,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACX,KAAK,CAAC,sBAAsB,EAAEU,GAAG,CAAC;;MAE1C;MACArB,YAAY,CAACuB,gBAAgB,CAAC,CAAC,CAAC;MAChCrB,UAAU,CAACsB,cAAc,CAAC,CAAC,CAAC;MAC5BpB,oBAAoB,CAACqB,wBAAwB,CAAC,CAAC,CAAC;IAClD,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAMrC,eAAe,CAAC6B,MAAM,CAAC,CAAC;MAC/C,MAAMS,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpD,MAAMC,UAAU,GAAGL,QAAQ,CAACP,IAAI,CAACa,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAKP,KAAK,CAAC;MAClEvB,oBAAoB,CAAC2B,UAAU,CAAC;IAClC,CAAC,CAAC,OAAOV,GAAG,EAAE;MACZC,OAAO,CAACX,KAAK,CAAC,oCAAoC,EAAEU,GAAG,CAAC;MACxDjB,oBAAoB,CAACqB,wBAAwB,CAAC,CAAC,CAAC;IAClD;EACF,CAAC;EAED,MAAMF,gBAAgB,GAAGA,CAAA,KAAM,CAC7B;IAAEY,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAwB,CAAC,EACxC;IAAED,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAsB,CAAC,EACtC;IAAED,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAqB,CAAC,CACtC;EAED,MAAMZ,cAAc,GAAGA,CAAA,KAAM,CAC3B;IAAEW,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,eAAe;IAAEC,SAAS,EAAE,YAAY;IAAEC,UAAU,EAAE;EAAE,CAAC,EACxE;IAAEH,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAE,oBAAoB;IAAEC,UAAU,EAAE;EAAE,CAAC,EAC/E;IAAEH,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAE,YAAY;IAAEC,UAAU,EAAE;EAAE,CAAC,EACtE;IAAEH,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,eAAe;IAAEC,SAAS,EAAE,UAAU;IAAEC,UAAU,EAAE;EAAE,CAAC,EACtE;IAAEH,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,eAAe;IAAEC,SAAS,EAAE,WAAW;IAAEC,UAAU,EAAE;EAAE,CAAC,EACvE;IAAEH,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAE,aAAa;IAAEC,UAAU,EAAE;EAAE,CAAC,EACvE;IAAEH,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,SAAS,EAAE,yBAAyB;IAAEC,UAAU,EAAE;EAAE,CAAC,EACvF;IAAEH,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,eAAe;IAAEC,SAAS,EAAE,aAAa;IAAEC,UAAU,EAAE;EAAE,CAAC,EACzE;IAAEH,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAE,YAAY;IAAEC,UAAU,EAAE;EAAE,CAAC,CACxE;EAED,MAAMb,wBAAwB,GAAGA,CAAA,KAAM,CACrC;IACEU,EAAE,EAAE,CAAC;IACLI,WAAW,EAAE,UAAU;IACvBC,UAAU,EAAE,mBAAmB;IAC/BC,YAAY,EAAE,uBAAuB;IACrCC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE;EACV,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLI,WAAW,EAAE,YAAY;IACzBC,UAAU,EAAE,kBAAkB;IAC9BC,YAAY,EAAE,uBAAuB;IACrCC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMC,mBAAmB,GAAG,MAAOC,QAAQ,IAAK;IAC9CrC,iBAAiB,CAAC,IAAI,CAAC;IACvBI,QAAQ,CAAC,EAAE,CAAC;IACZF,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMoC,eAAe,GAAG;QACtB,GAAGD,QAAQ;QACXF,MAAM,EAAE,WAAW;QACnBI,QAAQ,EAAE;MACZ,CAAC;MAED,MAAM1D,eAAe,CAAC2D,aAAa,CAACF,eAAe,CAAC;MAEpDpC,UAAU,CAAC,kCAAkC,CAAC;;MAE9C;MACA,MAAMU,sBAAsB,CAAC,CAAC;;MAE9B;MACA6B,UAAU,CAAC,MAAMvC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IAExC,CAAC,CAAC,OAAOW,GAAG,EAAE;MACZC,OAAO,CAACX,KAAK,CAAC,4BAA4B,EAAEU,GAAG,CAAC;;MAEhD;MACAX,UAAU,CAAC,8CAA8C,CAAC;MAC1DuC,UAAU,CAAC,MAAMvC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,SAAS;MACRF,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,IAAI,CAACX,cAAc,EAAE;IACnB,OAAO,IAAI,CAAC,CAAC;EACf;EAEA,oBACEJ,OAAA;IAAKyD,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtC1D,OAAA,CAACF,MAAM;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEV9D,OAAA;MAAKyD,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAC1D1D,OAAA;QAAKyD,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB1D,OAAA;UAAIyD,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzE9D,OAAA;UAAGyD,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAC,gBAAc,EAACvD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwC,IAAI;QAAA;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eAEN9D,OAAA;QAAKyD,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD1D,OAAA;UAAKyD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD1D,OAAA;YAAIyD,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEjF9C,OAAO,iBACNhB,OAAA;YAAKyD,SAAS,EAAC,4EAA4E;YAAAC,QAAA,EACxF1C;UAAO;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN,EAEA5C,KAAK,iBACJlB,OAAA;YAAKyD,SAAS,EAAC,sEAAsE;YAAAC,QAAA,EAClFxC;UAAK;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAlD,OAAO,gBACNZ,OAAA;YAAKyD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpD1D,OAAA;cAAKyD,SAAS,EAAC;YAAiE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC,gBAEN9D,OAAA,CAACH,eAAe;YACdS,SAAS,EAAEA,SAAU;YACrBE,OAAO,EAAEA,OAAQ;YACjBuD,QAAQ,EAAEZ,mBAAoB;YAC9BvC,OAAO,EAAEE,cAAe;YACxBkD,eAAe,EAAE;UAAK;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN9D,OAAA;UAAKyD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD1D,OAAA;YAAIyD,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAE7EpD,iBAAiB,CAACuD,MAAM,KAAK,CAAC,gBAC7BjE,OAAA;YAAKyD,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAEhD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,gBAEN9D,OAAA;YAAKyD,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBhD,iBAAiB,CAACwD,GAAG,CAAEC,WAAW,iBACjCnE,OAAA;cAA0ByD,SAAS,EAAC,uCAAuC;cAAAC,QAAA,eACzE1D,OAAA;gBAAKyD,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/C1D,OAAA;kBAAA0D,QAAA,gBACE1D,OAAA;oBAAIyD,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,EAAES,WAAW,CAACrB;kBAAW;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1E9D,OAAA;oBAAGyD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAES,WAAW,CAACpB;kBAAU;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjE9D,OAAA;oBAAGyD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAES,WAAW,CAACnB;kBAAY;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnE9D,OAAA;oBAAGyD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,QAAM,EAACS,WAAW,CAAClB,IAAI;kBAAA;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC,eACN9D,OAAA;kBAAMyD,SAAS,EAAE,4DACfU,WAAW,CAACjB,MAAM,KAAK,WAAW,GAC9B,+BAA+B,GAC/BiB,WAAW,CAACjB,MAAM,KAAK,WAAW,GAClC,6BAA6B,GAC7B,yBAAyB,EAC5B;kBAAAQ,QAAA,EACAS,WAAW,CAACjB;gBAAM;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC,GAjBEK,WAAW,CAACzB,EAAE;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkBnB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9D,OAAA;QAAKyD,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzD1D,OAAA;UAAKyD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChD1D,OAAA;YAAKyD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC1D,OAAA;cAAKyD,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B1D,OAAA;gBAAKyD,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,eAC9E1D,OAAA;kBAAKyD,SAAS,EAAC,oBAAoB;kBAACW,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAX,QAAA,eACzE1D,OAAA;oBAAMsE,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,oHAAoH;oBAACC,QAAQ,EAAC;kBAAS;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9D,OAAA;cAAKyD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9B1D,OAAA;gBAAA0D,QAAA,gBACE1D,OAAA;kBAAIyD,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7E9D,OAAA;kBAAIyD,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEhD,iBAAiB,CAACuD;gBAAM;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9D,OAAA;UAAKyD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChD1D,OAAA;YAAKyD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC1D,OAAA;cAAKyD,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B1D,OAAA;gBAAKyD,SAAS,EAAC,kEAAkE;gBAAAC,QAAA,eAC/E1D,OAAA;kBAAKyD,SAAS,EAAC,oBAAoB;kBAACW,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAX,QAAA,eACzE1D,OAAA;oBAAMuE,CAAC,EAAC;kBAA+C;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9D,OAAA;cAAKyD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9B1D,OAAA;gBAAA0D,QAAA,gBACE1D,OAAA;kBAAIyD,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzE9D,OAAA;kBAAIyD,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAC9ChD,iBAAiB,CAAC6B,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACU,MAAM,KAAK,WAAW,CAAC,CAACe;gBAAM;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9D,OAAA;UAAKyD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChD1D,OAAA;YAAKyD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC1D,OAAA;cAAKyD,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B1D,OAAA;gBAAKyD,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,eAChF1D,OAAA;kBAAKyD,SAAS,EAAC,oBAAoB;kBAACW,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAX,QAAA,eACzE1D,OAAA;oBAAMsE,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,wJAAwJ;oBAACC,QAAQ,EAAC;kBAAS;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9D,OAAA;cAAKyD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9B1D,OAAA;gBAAA0D,QAAA,gBACE1D,OAAA;kBAAIyD,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvE9D,OAAA;kBAAIyD,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAC9ChD,iBAAiB,CAAC6B,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACU,MAAM,KAAK,WAAW,CAAC,CAACe;gBAAM;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5D,EAAA,CAlRID,kBAAkB;EAAA,QACWR,OAAO,EACvBD,WAAW;AAAA;AAAAiF,EAAA,GAFxBxE,kBAAkB;AAoRxB,eAAeA,kBAAkB;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}