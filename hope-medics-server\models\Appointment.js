const db = require('./db');

class Appointment {
  static async create(appointmentData) {
    const { 
      patientId, 
      doctorId, 
      hospitalId, 
      date, 
      time, 
      status = 'Scheduled', 
      notes,
      patientName,
      patientEmail,
      patientPhone
    } = appointmentData;
    
    try {
      const query = `
        INSERT INTO Appointments 
        (patientId, doctorId, hospitalId, date, time, status, notes, patientName, patientEmail, patientPhone) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      const [result] = await db.promise().execute(query, [
        patientId, 
        doctorId, 
        hospitalId, 
        date, 
        time, 
        status, 
        notes,
        patientName,
        patientEmail,
        patientPhone
      ]);
      
      return {
        id: result.insertId,
        patientId,
        doctorId,
        hospitalId,
        date,
        time,
        status,
        notes,
        patientName,
        patientEmail,
        patientPhone
      };
    } catch (error) {
      throw error;
    }
  }

  static async findAll() {
    try {
      const query = `
        SELECT 
          a.*,
          u.name as patientName,
          u.email as patientEmail,
          d.name as doctor<PERSON><PERSON>,
          d.specialty as doctorSpecialty,
          h.name as hospitalName
        FROM Appointments a
        LEFT JOIN Users u ON a.patientId = u.id
        LEFT JOIN Doctors d ON a.doctorId = d.id
        LEFT JOIN Hospitals h ON a.hospitalId = h.id
        ORDER BY a.date DESC, a.time DESC
      `;
      const [rows] = await db.promise().execute(query);
      return rows;
    } catch (error) {
      throw error;
    }
  }

  static async findById(id) {
    try {
      const query = `
        SELECT 
          a.*,
          u.name as patientName,
          u.email as patientEmail,
          d.name as doctorName,
          d.specialty as doctorSpecialty,
          h.name as hospitalName
        FROM Appointments a
        LEFT JOIN Users u ON a.patientId = u.id
        LEFT JOIN Doctors d ON a.doctorId = d.id
        LEFT JOIN Hospitals h ON a.hospitalId = h.id
        WHERE a.id = ?
      `;
      const [rows] = await db.promise().execute(query, [id]);
      return rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  static async findByPatient(patientId) {
    try {
      const query = `
        SELECT 
          a.*,
          d.name as doctorName,
          d.specialty as doctorSpecialty,
          h.name as hospitalName
        FROM Appointments a
        LEFT JOIN Doctors d ON a.doctorId = d.id
        LEFT JOIN Hospitals h ON a.hospitalId = h.id
        WHERE a.patientId = ?
        ORDER BY a.date DESC, a.time DESC
      `;
      const [rows] = await db.promise().execute(query, [patientId]);
      return rows;
    } catch (error) {
      throw error;
    }
  }

  static async findByDoctor(doctorId) {
    try {
      const query = `
        SELECT 
          a.*,
          u.name as patientName,
          u.email as patientEmail,
          h.name as hospitalName
        FROM Appointments a
        LEFT JOIN Users u ON a.patientId = u.id
        LEFT JOIN Hospitals h ON a.hospitalId = h.id
        WHERE a.doctorId = ?
        ORDER BY a.date ASC, a.time ASC
      `;
      const [rows] = await db.promise().execute(query, [doctorId]);
      return rows;
    } catch (error) {
      throw error;
    }
  }

  static async findByHospital(hospitalId) {
    try {
      const query = `
        SELECT 
          a.*,
          u.name as patientName,
          u.email as patientEmail,
          d.name as doctorName,
          d.specialty as doctorSpecialty
        FROM Appointments a
        LEFT JOIN Users u ON a.patientId = u.id
        LEFT JOIN Doctors d ON a.doctorId = d.id
        WHERE a.hospitalId = ?
        ORDER BY a.date DESC, a.time DESC
      `;
      const [rows] = await db.promise().execute(query, [hospitalId]);
      return rows;
    } catch (error) {
      throw error;
    }
  }

  static async update(id, appointmentData) {
    try {
      const { date, time, status, notes } = appointmentData;
      const query = `
        UPDATE Appointments 
        SET date = ?, time = ?, status = ?, notes = ?
        WHERE id = ?
      `;
      await db.promise().execute(query, [date, time, status, notes, id]);
      
      return this.findById(id);
    } catch (error) {
      throw error;
    }
  }

  static async updateStatus(id, status) {
    try {
      const query = 'UPDATE Appointments SET status = ? WHERE id = ?';
      await db.promise().execute(query, [status, id]);
      
      return this.findById(id);
    } catch (error) {
      throw error;
    }
  }

  static async delete(id) {
    try {
      const query = 'DELETE FROM Appointments WHERE id = ?';
      const [result] = await db.promise().execute(query, [id]);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = Appointment;
