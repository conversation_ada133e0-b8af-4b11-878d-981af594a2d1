const jwt = require('jsonwebtoken');
const User = require('../models/User');

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

const authMiddleware = {
  // Verify JWT token
  async authenticate(req, res, next) {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({
          success: false,
          message: 'Access token is required'
        });
      }

      const token = authHeader.substring(7); // Remove 'Bearer ' prefix
      
      try {
        const decoded = jwt.verify(token, JWT_SECRET);
        const user = await User.findById(decoded.userId);
        
        if (!user) {
          return res.status(401).json({
            success: false,
            message: 'Invalid token - user not found'
          });
        }

        req.userId = user.id;
        req.user = user;
        next();
      } catch (jwtError) {
        return res.status(401).json({
          success: false,
          message: 'Invalid or expired token'
        });
      }
    } catch (error) {
      console.error('Authentication error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  // Check if user has admin role
  requireAdmin(req, res, next) {
    if (req.user && req.user.role === 'admin') {
      next();
    } else {
      res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }
  },

  // Check if user has doctor role
  requireDoctor(req, res, next) {
    if (req.user && req.user.role === 'doctor') {
      next();
    } else {
      res.status(403).json({
        success: false,
        message: 'Doctor access required'
      });
    }
  },

  // Check if user has receptionist role
  requireReceptionist(req, res, next) {
    if (req.user && req.user.role === 'receptionist') {
      next();
    } else {
      res.status(403).json({
        success: false,
        message: 'Receptionist access required'
      });
    }
  },

  // Check if user has patient role
  requirePatient(req, res, next) {
    if (req.user && req.user.role === 'patient') {
      next();
    } else {
      res.status(403).json({
        success: false,
        message: 'Patient access required'
      });
    }
  },

  // Check if user has admin or receptionist role
  requireAdminOrReceptionist(req, res, next) {
    if (req.user && (req.user.role === 'admin' || req.user.role === 'receptionist')) {
      next();
    } else {
      res.status(403).json({
        success: false,
        message: 'Admin or receptionist access required'
      });
    }
  },

  // Check if user has admin or doctor role
  requireAdminOrDoctor(req, res, next) {
    if (req.user && (req.user.role === 'admin' || req.user.role === 'doctor')) {
      next();
    } else {
      res.status(403).json({
        success: false,
        message: 'Admin or doctor access required'
      });
    }
  },

  // Optional authentication - doesn't fail if no token
  optionalAuth(req, res, next) {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next();
    }

    const token = authHeader.substring(7);
    
    try {
      const decoded = jwt.verify(token, JWT_SECRET);
      User.findById(decoded.userId)
        .then(user => {
          if (user) {
            req.userId = user.id;
            req.user = user;
          }
          next();
        })
        .catch(() => next());
    } catch (jwtError) {
      next();
    }
  }
};

module.exports = authMiddleware;
