{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\hopeMedics\\\\hopemedics\\\\src\\\\components\\\\HospitalCard.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HospitalCard = ({\n  hospital\n}) => {\n  const photos = hospital.photos ? JSON.parse(hospital.photos) : [];\n  const services = hospital.services ? JSON.parse(hospital.services) : [];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative h-48\",\n      children: [photos.length > 0 ? /*#__PURE__*/_jsxDEV(\"img\", {\n        src: photos[0],\n        alt: hospital.name,\n        className: \"w-full h-full object-cover\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full h-full bg-gray-200 flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-500\",\n          children: \"No Image Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-2 right-2 bg-primary-600 text-white px-2 py-1 rounded text-sm font-semibold\",\n        children: [\"$\", hospital.bedPricePerDay, \"/day\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-semibold text-gray-900 mb-2\",\n        children: hospital.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mb-2\",\n        children: hospital.address\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mb-3\",\n        children: hospital.contact\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), services.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-sm font-semibold text-gray-700 mb-1\",\n          children: \"Services:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-1\",\n          children: [services.slice(0, 3).map((service, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"bg-secondary-100 text-secondary-700 px-2 py-1 rounded text-xs\",\n            children: service\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 17\n          }, this)), services.length > 3 && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-500 text-xs\",\n            children: [\"+\", services.length - 3, \" more\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg font-bold text-primary-600\",\n          children: [\"Bed: $\", hospital.bedPricePerDay, \"/day\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: `/hospital/${hospital.id}`,\n          className: \"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors duration-200\",\n          children: \"View Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_c = HospitalCard;\nexport default HospitalCard;\nvar _c;\n$RefreshReg$(_c, \"HospitalCard\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "HospitalCard", "hospital", "photos", "JSON", "parse", "services", "className", "children", "length", "src", "alt", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "bedPricePerDay", "address", "contact", "slice", "map", "service", "index", "to", "id", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/hopeMedics/hopemedics/src/components/HospitalCard.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst HospitalCard = ({ hospital }) => {\n  const photos = hospital.photos ? JSON.parse(hospital.photos) : [];\n  const services = hospital.services ? JSON.parse(hospital.services) : [];\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300\">\n      <div className=\"relative h-48\">\n        {photos.length > 0 ? (\n          <img\n            src={photos[0]}\n            alt={hospital.name}\n            className=\"w-full h-full object-cover\"\n          />\n        ) : (\n          <div className=\"w-full h-full bg-gray-200 flex items-center justify-center\">\n            <span className=\"text-gray-500\">No Image Available</span>\n          </div>\n        )}\n        <div className=\"absolute top-2 right-2 bg-primary-600 text-white px-2 py-1 rounded text-sm font-semibold\">\n          ${hospital.bedPricePerDay}/day\n        </div>\n      </div>\n      \n      <div className=\"p-4\">\n        <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">{hospital.name}</h3>\n        <p className=\"text-gray-600 mb-2\">{hospital.address}</p>\n        <p className=\"text-gray-600 mb-3\">{hospital.contact}</p>\n        \n        {services.length > 0 && (\n          <div className=\"mb-3\">\n            <h4 className=\"text-sm font-semibold text-gray-700 mb-1\">Services:</h4>\n            <div className=\"flex flex-wrap gap-1\">\n              {services.slice(0, 3).map((service, index) => (\n                <span\n                  key={index}\n                  className=\"bg-secondary-100 text-secondary-700 px-2 py-1 rounded text-xs\"\n                >\n                  {service}\n                </span>\n              ))}\n              {services.length > 3 && (\n                <span className=\"text-gray-500 text-xs\">+{services.length - 3} more</span>\n              )}\n            </div>\n          </div>\n        )}\n        \n        <div className=\"flex justify-between items-center\">\n          <div className=\"text-lg font-bold text-primary-600\">\n            Bed: ${hospital.bedPricePerDay}/day\n          </div>\n          <Link\n            to={`/hospital/${hospital.id}`}\n            className=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors duration-200\"\n          >\n            View Details\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default HospitalCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EACrC,MAAMC,MAAM,GAAGD,QAAQ,CAACC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAACC,MAAM,CAAC,GAAG,EAAE;EACjE,MAAMG,QAAQ,GAAGJ,QAAQ,CAACI,QAAQ,GAAGF,IAAI,CAACC,KAAK,CAACH,QAAQ,CAACI,QAAQ,CAAC,GAAG,EAAE;EAEvE,oBACEN,OAAA;IAAKO,SAAS,EAAC,8FAA8F;IAAAC,QAAA,gBAC3GR,OAAA;MAAKO,SAAS,EAAC,eAAe;MAAAC,QAAA,GAC3BL,MAAM,CAACM,MAAM,GAAG,CAAC,gBAChBT,OAAA;QACEU,GAAG,EAAEP,MAAM,CAAC,CAAC,CAAE;QACfQ,GAAG,EAAET,QAAQ,CAACU,IAAK;QACnBL,SAAS,EAAC;MAA4B;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,gBAEFhB,OAAA;QAAKO,SAAS,EAAC,4DAA4D;QAAAC,QAAA,eACzER,OAAA;UAAMO,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAkB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CACN,eACDhB,OAAA;QAAKO,SAAS,EAAC,0FAA0F;QAAAC,QAAA,GAAC,GACvG,EAACN,QAAQ,CAACe,cAAc,EAAC,MAC5B;MAAA;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhB,OAAA;MAAKO,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAClBR,OAAA;QAAIO,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAEN,QAAQ,CAACU;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC7EhB,OAAA;QAAGO,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAEN,QAAQ,CAACgB;MAAO;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxDhB,OAAA;QAAGO,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAEN,QAAQ,CAACiB;MAAO;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAEvDV,QAAQ,CAACG,MAAM,GAAG,CAAC,iBAClBT,OAAA;QAAKO,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBR,OAAA;UAAIO,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvEhB,OAAA;UAAKO,SAAS,EAAC,sBAAsB;UAAAC,QAAA,GAClCF,QAAQ,CAACc,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACvCvB,OAAA;YAEEO,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAExEc;UAAO,GAHHC,KAAK;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIN,CACP,CAAC,EACDV,QAAQ,CAACG,MAAM,GAAG,CAAC,iBAClBT,OAAA;YAAMO,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,GAAC,EAACF,QAAQ,CAACG,MAAM,GAAG,CAAC,EAAC,OAAK;UAAA;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC1E;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAEDhB,OAAA;QAAKO,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDR,OAAA;UAAKO,SAAS,EAAC,oCAAoC;UAAAC,QAAA,GAAC,QAC5C,EAACN,QAAQ,CAACe,cAAc,EAAC,MACjC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNhB,OAAA,CAACF,IAAI;UACH0B,EAAE,EAAE,aAAatB,QAAQ,CAACuB,EAAE,EAAG;UAC/BlB,SAAS,EAAC,qHAAqH;UAAAC,QAAA,EAChI;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACU,EAAA,GA7DIzB,YAAY;AA+DlB,eAAeA,YAAY;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}