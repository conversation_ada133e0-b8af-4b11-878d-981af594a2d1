-- Hope Medics Database Initialization Script
-- Run this script to create the database and insert sample data

-- <PERSON>reate database
CREATE DATABASE IF NOT EXISTS hopemedics;
USE hopemedics;

-- Create Users table
CREATE TABLE IF NOT EXISTS Users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  role ENUM('patient', 'doctor', 'admin', 'receptionist') DEFAULT 'patient',
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create Hospitals table
CREATE TABLE IF NOT EXISTS Hospitals (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  address TEXT NOT NULL,
  contact VARCHAR(50) NOT NULL,
  bedPricePerDay DECIMAL(10,2) NOT NULL,
  photos TEXT,
  services TEXT,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create Doctors table
CREATE TABLE IF NOT EXISTS Doctors (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  specialty VARCHAR(100) NOT NULL,
  hospitalId INT,
  userId INT,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (hospitalId) REFERENCES Hospitals(id) ON DELETE SET NULL,
  FOREIGN KEY (userId) REFERENCES Users(id) ON DELETE SET NULL
);

-- Create Appointments table
CREATE TABLE IF NOT EXISTS Appointments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  patientId INT,
  doctorId INT,
  hospitalId INT,
  date DATE NOT NULL,
  time VARCHAR(10) NOT NULL,
  status VARCHAR(20) DEFAULT 'Scheduled',
  notes TEXT,
  patientName VARCHAR(100),
  patientEmail VARCHAR(100),
  patientPhone VARCHAR(20),
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (patientId) REFERENCES Users(id) ON DELETE CASCADE,
  FOREIGN KEY (doctorId) REFERENCES Doctors(id) ON DELETE CASCADE,
  FOREIGN KEY (hospitalId) REFERENCES Hospitals(id) ON DELETE CASCADE
);

-- Insert sample users (passwords are hashed for 'password')
INSERT INTO Users (name, email, password, role) VALUES
('Admin User', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin'),
('Dr. Sarah Johnson', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'doctor'),
('Reception Staff', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'receptionist'),
('John Patient', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'patient'),
('Dr. Michael Chen', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'doctor'),
('Dr. Emily Davis', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'doctor');

-- Insert sample hospitals
INSERT INTO Hospitals (name, address, contact, bedPricePerDay, photos, services) VALUES
(
  'City General Hospital',
  '123 Main St, Downtown',
  '(555) 123-4567',
  150.00,
  '["https://via.placeholder.com/800x400?text=City+General+Hospital+Main", "https://via.placeholder.com/400x300?text=Emergency+Room", "https://via.placeholder.com/400x300?text=Patient+Room"]',
  '["Emergency Care", "Surgery", "Cardiology", "Pediatrics", "Radiology", "Laboratory"]'
),
(
  'Hope Medical Center',
  '456 Oak Ave, Midtown',
  '(555) 987-6543',
  200.00,
  '["https://via.placeholder.com/800x400?text=Hope+Medical+Center", "https://via.placeholder.com/400x300?text=Surgery+Suite", "https://via.placeholder.com/400x300?text=ICU"]',
  '["Oncology", "Neurology", "Orthopedics", "Radiology", "Pharmacy", "Rehabilitation"]'
),
(
  'Sunrise Healthcare',
  '789 Pine Rd, Uptown',
  '(555) 456-7890',
  175.00,
  '["https://via.placeholder.com/800x400?text=Sunrise+Healthcare", "https://via.placeholder.com/400x300?text=Maternity+Ward", "https://via.placeholder.com/400x300?text=Therapy+Center"]',
  '["Maternity", "Dermatology", "Psychiatry", "Physical Therapy", "Nutrition", "Wellness"]'
);

-- Insert sample doctors
INSERT INTO Doctors (name, specialty, hospitalId, userId) VALUES
('Sarah Johnson', 'Cardiology', 1, 2),
('Michael Chen', 'Emergency Medicine', 1, 5),
('Emily Davis', 'Pediatrics', 1, 6),
('Robert Wilson', 'Oncology', 2, NULL),
('Lisa Anderson', 'Neurology', 2, NULL),
('David Brown', 'Orthopedics', 2, NULL),
('Jennifer Taylor', 'Obstetrics & Gynecology', 3, NULL),
('Mark Thompson', 'Dermatology', 3, NULL),
('Amanda White', 'Psychiatry', 3, NULL);

-- Insert sample appointments
INSERT INTO Appointments (patientId, doctorId, hospitalId, date, time, status, notes) VALUES
(4, 1, 1, CURDATE(), '10:00', 'Scheduled', 'Regular checkup'),
(4, 2, 1, CURDATE(), '14:30', 'Scheduled', 'Follow-up appointment'),
(4, 4, 2, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '09:00', 'Scheduled', 'Initial consultation'),
(4, 7, 3, DATE_ADD(CURDATE(), INTERVAL 2 DAY), '11:30', 'Scheduled', 'Lab results review'),
(4, 1, 1, DATE_SUB(CURDATE(), INTERVAL 1 DAY), '15:00', 'Completed', 'Treatment completed successfully');

-- Insert some walk-in appointments (no patientId)
INSERT INTO Appointments (patientId, doctorId, hospitalId, date, time, status, notes, patientName, patientEmail, patientPhone) VALUES
(NULL, 3, 1, CURDATE(), '16:00', 'Scheduled', 'Walk-in patient', 'Alice Walker', '<EMAIL>', '(*************'),
(NULL, 8, 3, CURDATE(), '13:00', 'Completed', 'Skin consultation', 'Bob Smith', '<EMAIL>', '(*************');

COMMIT;
