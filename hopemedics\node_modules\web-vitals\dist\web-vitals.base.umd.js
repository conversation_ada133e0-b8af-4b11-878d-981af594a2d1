!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).webVitals=e.webVitals||{})}(this,(function(e){"use strict";var t=function(e,t){return{name:e,value:void 0===t?-1:t,delta:0,entries:[],id:"v2-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12)}},n=function(e,t){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){if("first-input"===e&&!("PerformanceEventTiming"in self))return;var n=new PerformanceObserver((function(e){return e.getEntries().map(t)}));return n.observe({type:e,buffered:!0}),n}}catch(e){}},i=function(e,t){var n=function n(i){"pagehide"!==i.type&&"hidden"!==document.visibilityState||(e(i),t&&(removeEventListener("visibilitychange",n,!0),removeEventListener("pagehide",n,!0)))};addEventListener("visibilitychange",n,!0),addEventListener("pagehide",n,!0)},r=function(e){addEventListener("pageshow",(function(t){t.persisted&&e(t)}),!0)},a=function(e,t,n){var i;return function(r){t.value>=0&&(r||n)&&(t.delta=t.value-(i||0),(t.delta||void 0===i)&&(i=t.value,e(t)))}},o=-1,u=function(){i((function(e){var t=e.timeStamp;o=t}),!0)},f=function(){return o<0&&((o=window.webVitals.firstHiddenTime)===1/0&&u(),r((function(){setTimeout((function(){o="hidden"===document.visibilityState?0:1/0,u()}),0)}))),{get firstHiddenTime(){return o}}},s=function(e,i){var o,u=f(),s=t("FCP"),c=function(e){"first-contentful-paint"===e.name&&(m&&m.disconnect(),e.startTime<u.firstHiddenTime&&(s.value=e.startTime,s.entries.push(e),o(!0)))},d=window.performance&&performance.getEntriesByName&&performance.getEntriesByName("first-contentful-paint")[0],m=d?null:n("paint",c);(d||m)&&(o=a(e,s,i),d&&c(d),r((function(n){s=t("FCP"),o=a(e,s,i),requestAnimationFrame((function(){requestAnimationFrame((function(){s.value=performance.now()-n.timeStamp,o(!0)}))}))})))},c=!1,d=-1,m={};e.getCLS=function(e,o){c||(s((function(e){d=e.value})),c=!0);var u,f=function(t){d>-1&&e(t)},m=t("CLS",0),l=0,v=[],p=function(e){if(!e.hadRecentInput){var t=v[0],n=v[v.length-1];l&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(l+=e.value,v.push(e)):(l=e.value,v=[e]),l>m.value&&(m.value=l,m.entries=v,u())}},T=n("layout-shift",p);T&&(u=a(f,m,o),i((function(){T.takeRecords().map(p),u(!0)})),r((function(){l=0,d=-1,m=t("CLS",0),u=a(f,m,o)})))},e.getFCP=s,e.getFID=function(e,o){var u,s=f(),c=t("FID"),d=function(e){e.startTime<s.firstHiddenTime&&(c.value=e.processingStart-e.startTime,c.entries.push(e),u(!0))},m=n("first-input",d);u=a(e,c,o),m&&i((function(){m.takeRecords().map(d),m.disconnect()}),!0),m||window.webVitals.firstInputPolyfill(d),r((function(){c=t("FID"),u=a(e,c,o),window.webVitals.resetFirstInputPolyfill(),window.webVitals.firstInputPolyfill(d)}))},e.getLCP=function(e,o){var u,s=f(),c=t("LCP"),d=function(e){var t=e.startTime;t<s.firstHiddenTime&&(c.value=t,c.entries.push(e),u())},l=n("largest-contentful-paint",d);if(l){u=a(e,c,o);var v=function(){m[c.id]||(l.takeRecords().map(d),l.disconnect(),m[c.id]=!0,u(!0))};["keydown","click"].forEach((function(e){addEventListener(e,v,{once:!0,capture:!0})})),i(v,!0),r((function(n){c=t("LCP"),u=a(e,c,o),requestAnimationFrame((function(){requestAnimationFrame((function(){c.value=performance.now()-n.timeStamp,m[c.id]=!0,u(!0)}))}))}))}},e.getTTFB=function(e){var n,i=t("TTFB");n=function(){try{var t=performance.getEntriesByType("navigation")[0]||function(){var e=performance.timing,t={entryType:"navigation",startTime:0};for(var n in e)"navigationStart"!==n&&"toJSON"!==n&&(t[n]=Math.max(e[n]-e.navigationStart,0));return t}();if(i.value=i.delta=t.responseStart,i.value<0||i.value>performance.now())return;i.entries=[t],e(i)}catch(e){}},"complete"===document.readyState?setTimeout(n,0):addEventListener("load",(function(){return setTimeout(n,0)}))},Object.defineProperty(e,"__esModule",{value:!0})}));
