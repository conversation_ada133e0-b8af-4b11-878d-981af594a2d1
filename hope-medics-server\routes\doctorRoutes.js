const express = require('express');
const router = express.Router();
const doctorController = require('../controllers/doctorController');
const { authenticate, requireAdmin, optionalAuth } = require('../middleware/auth');

// Public routes
router.get('/', optionalAuth, doctorController.getAllDoctors);
router.get('/:id', optionalAuth, doctorController.getDoctorById);
router.get('/hospital/:hospitalId', optionalAuth, doctorController.getDoctorsByHospital);

// Admin only routes
router.post('/', authenticate, requireAdmin, doctorController.createDoctor);
router.put('/:id', authenticate, requireAdmin, doctorController.updateDoctor);
router.delete('/:id', authenticate, requireAdmin, doctorController.deleteDoctor);

module.exports = router;
