{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\hopeMedics\\\\hopemedics\\\\src\\\\pages\\\\DoctorDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { appointmentsAPI } from '../services/api';\nimport Navbar from '../components/Navbar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DoctorDashboard = () => {\n  _s();\n  const {\n    user,\n    isDoctor\n  } = useAuth();\n  const navigate = useNavigate();\n  const [appointments, setAppointments] = useState([]);\n  const [filteredAppointments, setFilteredAppointments] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filter, setFilter] = useState('today'); // today, upcoming, all\n  const [stats, setStats] = useState({\n    today: 0,\n    upcoming: 0,\n    total: 0\n  });\n  useEffect(() => {\n    if (!isDoctor) {\n      navigate('/');\n      return;\n    }\n    fetchAppointments();\n  }, [isDoctor, navigate]);\n  useEffect(() => {\n    filterAppointments();\n  }, [appointments, filter]);\n  const fetchAppointments = async () => {\n    try {\n      setLoading(true);\n\n      // In a real app, this would be the doctor's ID from the user object\n      const doctorId = (user === null || user === void 0 ? void 0 : user.doctorId) || 1;\n      const response = await appointmentsAPI.getByDoctor(doctorId);\n      setAppointments(response.data);\n      calculateStats(response.data);\n    } catch (err) {\n      console.error('Error fetching appointments:', err);\n\n      // Mock data for development\n      const mockAppointments = getMockAppointments();\n      setAppointments(mockAppointments);\n      calculateStats(mockAppointments);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getMockAppointments = () => [{\n    id: 1,\n    patientName: 'John Doe',\n    patientEmail: '<EMAIL>',\n    date: new Date().toISOString().split('T')[0],\n    // Today\n    time: '10:00',\n    status: 'Scheduled',\n    notes: 'Regular checkup'\n  }, {\n    id: 2,\n    patientName: 'Jane Smith',\n    patientEmail: '<EMAIL>',\n    date: new Date().toISOString().split('T')[0],\n    // Today\n    time: '14:30',\n    status: 'Scheduled',\n    notes: 'Follow-up appointment'\n  }, {\n    id: 3,\n    patientName: 'Bob Wilson',\n    patientEmail: '<EMAIL>',\n    date: new Date(Date.now() + 86400000).toISOString().split('T')[0],\n    // Tomorrow\n    time: '09:00',\n    status: 'Scheduled',\n    notes: 'Initial consultation'\n  }, {\n    id: 4,\n    patientName: 'Alice Brown',\n    patientEmail: '<EMAIL>',\n    date: new Date(Date.now() + 172800000).toISOString().split('T')[0],\n    // Day after tomorrow\n    time: '11:30',\n    status: 'Scheduled',\n    notes: 'Lab results review'\n  }, {\n    id: 5,\n    patientName: 'Charlie Davis',\n    patientEmail: '<EMAIL>',\n    date: new Date(Date.now() - 86400000).toISOString().split('T')[0],\n    // Yesterday\n    time: '15:00',\n    status: 'Completed',\n    notes: 'Treatment completed successfully'\n  }];\n  const calculateStats = appointmentList => {\n    const today = new Date().toISOString().split('T')[0];\n    const todayAppointments = appointmentList.filter(apt => apt.date === today);\n    const upcomingAppointments = appointmentList.filter(apt => apt.date > today);\n    setStats({\n      today: todayAppointments.length,\n      upcoming: upcomingAppointments.length,\n      total: appointmentList.length\n    });\n  };\n  const filterAppointments = () => {\n    const today = new Date().toISOString().split('T')[0];\n    let filtered = [];\n    switch (filter) {\n      case 'today':\n        filtered = appointments.filter(apt => apt.date === today);\n        break;\n      case 'upcoming':\n        filtered = appointments.filter(apt => apt.date >= today);\n        break;\n      default:\n        filtered = appointments;\n    }\n\n    // Sort by date and time\n    filtered.sort((a, b) => {\n      if (a.date === b.date) {\n        return a.time.localeCompare(b.time);\n      }\n      return a.date.localeCompare(b.date);\n    });\n    setFilteredAppointments(filtered);\n  };\n  const updateAppointmentStatus = async (appointmentId, newStatus) => {\n    try {\n      await appointmentsAPI.update(appointmentId, {\n        status: newStatus\n      });\n\n      // Update local state\n      setAppointments(prev => prev.map(apt => apt.id === appointmentId ? {\n        ...apt,\n        status: newStatus\n      } : apt));\n    } catch (err) {\n      console.error('Error updating appointment status:', err);\n\n      // For development, update locally\n      setAppointments(prev => prev.map(apt => apt.id === appointmentId ? {\n        ...apt,\n        status: newStatus\n      } : apt));\n    }\n  };\n  if (!isDoctor) {\n    return null; // Will redirect\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center items-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900\",\n          children: \"Doctor Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-white\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"Today's Appointments\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: stats.today\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-white\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"Upcoming Appointments\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: stats.upcoming\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-white\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"Total Appointments\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: stats.total\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-lg font-medium text-gray-900\",\n              children: \"Appointments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setFilter('today'),\n                className: `px-3 py-1 rounded text-sm font-medium ${filter === 'today' ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n                children: \"Today\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setFilter('upcoming'),\n                className: `px-3 py-1 rounded text-sm font-medium ${filter === 'upcoming' ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n                children: \"Upcoming\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setFilter('all'),\n                className: `px-3 py-1 rounded text-sm font-medium ${filter === 'all' ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n                children: \"All\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"divide-y divide-gray-200\",\n          children: filteredAppointments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 py-8 text-center text-gray-500\",\n            children: \"No appointments found for the selected filter.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this) : filteredAppointments.map(appointment => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 py-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-medium text-gray-900\",\n                      children: appointment.patientName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 303,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: appointment.patientEmail\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 306,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [appointment.date, \" at \", appointment.time]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 309,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 23\n                }, this), appointment.notes && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-2 text-sm text-gray-600\",\n                  children: appointment.notes\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${appointment.status === 'Scheduled' ? 'bg-yellow-100 text-yellow-800' : appointment.status === 'Completed' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                  children: appointment.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 23\n                }, this), appointment.status === 'Scheduled' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => updateAppointmentStatus(appointment.id, 'Completed'),\n                    className: \"bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs font-medium\",\n                    children: \"Complete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => updateAppointmentStatus(appointment.id, 'Cancelled'),\n                    className: \"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs font-medium\",\n                    children: \"Cancel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 19\n            }, this)\n          }, appointment.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 185,\n    columnNumber: 5\n  }, this);\n};\n_s(DoctorDashboard, \"a5sKVyhjCMZQ84F8t3N+erbDZzo=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = DoctorDashboard;\nexport default DoctorDashboard;\nvar _c;\n$RefreshReg$(_c, \"DoctorDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useAuth", "appointmentsAPI", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "DoctorDashboard", "_s", "user", "isDoctor", "navigate", "appointments", "setAppointments", "filteredAppointments", "setFilteredAppointments", "loading", "setLoading", "filter", "setFilter", "stats", "setStats", "today", "upcoming", "total", "fetchAppointments", "filterAppointments", "doctorId", "response", "getByDoctor", "data", "calculateStats", "err", "console", "error", "mockAppointments", "getMockAppointments", "id", "patientName", "patientEmail", "date", "Date", "toISOString", "split", "time", "status", "notes", "now", "appointmentList", "todayAppointments", "apt", "upcomingAppointments", "length", "filtered", "sort", "a", "b", "localeCompare", "updateAppointmentStatus", "appointmentId", "newStatus", "update", "prev", "map", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "fill", "viewBox", "fillRule", "d", "clipRule", "onClick", "appointment", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/hopeMedics/hopemedics/src/pages/DoctorDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { appointmentsAPI } from '../services/api';\nimport Navbar from '../components/Navbar';\n\nconst DoctorDashboard = () => {\n  const { user, isDoctor } = useAuth();\n  const navigate = useNavigate();\n  \n  const [appointments, setAppointments] = useState([]);\n  const [filteredAppointments, setFilteredAppointments] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filter, setFilter] = useState('today'); // today, upcoming, all\n  const [stats, setStats] = useState({\n    today: 0,\n    upcoming: 0,\n    total: 0\n  });\n\n  useEffect(() => {\n    if (!isDoctor) {\n      navigate('/');\n      return;\n    }\n    \n    fetchAppointments();\n  }, [isDoctor, navigate]);\n\n  useEffect(() => {\n    filterAppointments();\n  }, [appointments, filter]);\n\n  const fetchAppointments = async () => {\n    try {\n      setLoading(true);\n      \n      // In a real app, this would be the doctor's ID from the user object\n      const doctorId = user?.doctorId || 1;\n      const response = await appointmentsAPI.getByDoctor(doctorId);\n      \n      setAppointments(response.data);\n      calculateStats(response.data);\n      \n    } catch (err) {\n      console.error('Error fetching appointments:', err);\n      \n      // Mock data for development\n      const mockAppointments = getMockAppointments();\n      setAppointments(mockAppointments);\n      calculateStats(mockAppointments);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getMockAppointments = () => [\n    {\n      id: 1,\n      patientName: 'John Doe',\n      patientEmail: '<EMAIL>',\n      date: new Date().toISOString().split('T')[0], // Today\n      time: '10:00',\n      status: 'Scheduled',\n      notes: 'Regular checkup'\n    },\n    {\n      id: 2,\n      patientName: 'Jane Smith',\n      patientEmail: '<EMAIL>',\n      date: new Date().toISOString().split('T')[0], // Today\n      time: '14:30',\n      status: 'Scheduled',\n      notes: 'Follow-up appointment'\n    },\n    {\n      id: 3,\n      patientName: 'Bob Wilson',\n      patientEmail: '<EMAIL>',\n      date: new Date(Date.now() + 86400000).toISOString().split('T')[0], // Tomorrow\n      time: '09:00',\n      status: 'Scheduled',\n      notes: 'Initial consultation'\n    },\n    {\n      id: 4,\n      patientName: 'Alice Brown',\n      patientEmail: '<EMAIL>',\n      date: new Date(Date.now() + 172800000).toISOString().split('T')[0], // Day after tomorrow\n      time: '11:30',\n      status: 'Scheduled',\n      notes: 'Lab results review'\n    },\n    {\n      id: 5,\n      patientName: 'Charlie Davis',\n      patientEmail: '<EMAIL>',\n      date: new Date(Date.now() - 86400000).toISOString().split('T')[0], // Yesterday\n      time: '15:00',\n      status: 'Completed',\n      notes: 'Treatment completed successfully'\n    }\n  ];\n\n  const calculateStats = (appointmentList) => {\n    const today = new Date().toISOString().split('T')[0];\n    const todayAppointments = appointmentList.filter(apt => apt.date === today);\n    const upcomingAppointments = appointmentList.filter(apt => apt.date > today);\n    \n    setStats({\n      today: todayAppointments.length,\n      upcoming: upcomingAppointments.length,\n      total: appointmentList.length\n    });\n  };\n\n  const filterAppointments = () => {\n    const today = new Date().toISOString().split('T')[0];\n    \n    let filtered = [];\n    switch (filter) {\n      case 'today':\n        filtered = appointments.filter(apt => apt.date === today);\n        break;\n      case 'upcoming':\n        filtered = appointments.filter(apt => apt.date >= today);\n        break;\n      default:\n        filtered = appointments;\n    }\n    \n    // Sort by date and time\n    filtered.sort((a, b) => {\n      if (a.date === b.date) {\n        return a.time.localeCompare(b.time);\n      }\n      return a.date.localeCompare(b.date);\n    });\n    \n    setFilteredAppointments(filtered);\n  };\n\n  const updateAppointmentStatus = async (appointmentId, newStatus) => {\n    try {\n      await appointmentsAPI.update(appointmentId, { status: newStatus });\n      \n      // Update local state\n      setAppointments(prev => \n        prev.map(apt => \n          apt.id === appointmentId \n            ? { ...apt, status: newStatus }\n            : apt\n        )\n      );\n    } catch (err) {\n      console.error('Error updating appointment status:', err);\n      \n      // For development, update locally\n      setAppointments(prev => \n        prev.map(apt => \n          apt.id === appointmentId \n            ? { ...apt, status: newStatus }\n            : apt\n        )\n      );\n    }\n  };\n\n  if (!isDoctor) {\n    return null; // Will redirect\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navbar />\n        <div className=\"flex justify-center items-center h-64\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n      \n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">Doctor Dashboard</h1>\n          <p className=\"text-gray-600\">Welcome back, {user?.name}</p>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\" clipRule=\"evenodd\"/>\n                  </svg>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Today's Appointments</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{stats.today}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z\" clipRule=\"evenodd\"/>\n                  </svg>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Upcoming Appointments</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{stats.upcoming}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"/>\n                  </svg>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Appointments</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{stats.total}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Appointments List */}\n        <div className=\"bg-white rounded-lg shadow-md\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <div className=\"flex justify-between items-center\">\n              <h2 className=\"text-lg font-medium text-gray-900\">Appointments</h2>\n              <div className=\"flex space-x-2\">\n                <button\n                  onClick={() => setFilter('today')}\n                  className={`px-3 py-1 rounded text-sm font-medium ${\n                    filter === 'today'\n                      ? 'bg-primary-600 text-white'\n                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n                  }`}\n                >\n                  Today\n                </button>\n                <button\n                  onClick={() => setFilter('upcoming')}\n                  className={`px-3 py-1 rounded text-sm font-medium ${\n                    filter === 'upcoming'\n                      ? 'bg-primary-600 text-white'\n                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n                  }`}\n                >\n                  Upcoming\n                </button>\n                <button\n                  onClick={() => setFilter('all')}\n                  className={`px-3 py-1 rounded text-sm font-medium ${\n                    filter === 'all'\n                      ? 'bg-primary-600 text-white'\n                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n                  }`}\n                >\n                  All\n                </button>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"divide-y divide-gray-200\">\n            {filteredAppointments.length === 0 ? (\n              <div className=\"px-6 py-8 text-center text-gray-500\">\n                No appointments found for the selected filter.\n              </div>\n            ) : (\n              filteredAppointments.map((appointment) => (\n                <div key={appointment.id} className=\"px-6 py-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center space-x-4\">\n                        <div>\n                          <h3 className=\"text-lg font-medium text-gray-900\">\n                            {appointment.patientName}\n                          </h3>\n                          <p className=\"text-sm text-gray-500\">{appointment.patientEmail}</p>\n                        </div>\n                        <div className=\"text-sm text-gray-500\">\n                          <p>{appointment.date} at {appointment.time}</p>\n                        </div>\n                      </div>\n                      {appointment.notes && (\n                        <p className=\"mt-2 text-sm text-gray-600\">{appointment.notes}</p>\n                      )}\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-3\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                        appointment.status === 'Scheduled' \n                          ? 'bg-yellow-100 text-yellow-800'\n                          : appointment.status === 'Completed'\n                          ? 'bg-green-100 text-green-800'\n                          : 'bg-red-100 text-red-800'\n                      }`}>\n                        {appointment.status}\n                      </span>\n                      \n                      {appointment.status === 'Scheduled' && (\n                        <div className=\"flex space-x-2\">\n                          <button\n                            onClick={() => updateAppointmentStatus(appointment.id, 'Completed')}\n                            className=\"bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs font-medium\"\n                          >\n                            Complete\n                          </button>\n                          <button\n                            onClick={() => updateAppointmentStatus(appointment.id, 'Cancelled')}\n                            className=\"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs font-medium\"\n                          >\n                            Cancel\n                          </button>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DoctorDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,eAAe,QAAQ,iBAAiB;AACjD,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC,IAAI;IAAEC;EAAS,CAAC,GAAGR,OAAO,CAAC,CAAC;EACpC,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACe,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC;IACjCuB,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE;EACT,CAAC,CAAC;EAEFxB,SAAS,CAAC,MAAM;IACd,IAAI,CAACU,QAAQ,EAAE;MACbC,QAAQ,CAAC,GAAG,CAAC;MACb;IACF;IAEAc,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACf,QAAQ,EAAEC,QAAQ,CAAC,CAAC;EAExBX,SAAS,CAAC,MAAM;IACd0B,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACd,YAAY,EAAEM,MAAM,CAAC,CAAC;EAE1B,MAAMO,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFR,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMU,QAAQ,GAAG,CAAAlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,QAAQ,KAAI,CAAC;MACpC,MAAMC,QAAQ,GAAG,MAAMzB,eAAe,CAAC0B,WAAW,CAACF,QAAQ,CAAC;MAE5Dd,eAAe,CAACe,QAAQ,CAACE,IAAI,CAAC;MAC9BC,cAAc,CAACH,QAAQ,CAACE,IAAI,CAAC;IAE/B,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEF,GAAG,CAAC;;MAElD;MACA,MAAMG,gBAAgB,GAAGC,mBAAmB,CAAC,CAAC;MAC9CvB,eAAe,CAACsB,gBAAgB,CAAC;MACjCJ,cAAc,CAACI,gBAAgB,CAAC;IAClC,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmB,mBAAmB,GAAGA,CAAA,KAAM,CAChC;IACEC,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,UAAU;IACvBC,YAAY,EAAE,kBAAkB;IAChCC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAE;IAC9CC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE,WAAW;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,YAAY;IACzBC,YAAY,EAAE,kBAAkB;IAChCC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAE;IAC9CC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE,WAAW;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,YAAY;IACzBC,YAAY,EAAE,iBAAiB;IAC/BC,IAAI,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACM,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAACL,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAE;IACnEC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE,WAAW;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,aAAa;IAC1BC,YAAY,EAAE,mBAAmB;IACjCC,IAAI,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACM,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAACL,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAE;IACpEC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE,WAAW;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,eAAe;IAC5BC,YAAY,EAAE,qBAAqB;IACnCC,IAAI,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACM,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAACL,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAE;IACnEC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE,WAAW;IACnBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMf,cAAc,GAAIiB,eAAe,IAAK;IAC1C,MAAM1B,KAAK,GAAG,IAAImB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpD,MAAMM,iBAAiB,GAAGD,eAAe,CAAC9B,MAAM,CAACgC,GAAG,IAAIA,GAAG,CAACV,IAAI,KAAKlB,KAAK,CAAC;IAC3E,MAAM6B,oBAAoB,GAAGH,eAAe,CAAC9B,MAAM,CAACgC,GAAG,IAAIA,GAAG,CAACV,IAAI,GAAGlB,KAAK,CAAC;IAE5ED,QAAQ,CAAC;MACPC,KAAK,EAAE2B,iBAAiB,CAACG,MAAM;MAC/B7B,QAAQ,EAAE4B,oBAAoB,CAACC,MAAM;MACrC5B,KAAK,EAAEwB,eAAe,CAACI;IACzB,CAAC,CAAC;EACJ,CAAC;EAED,MAAM1B,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMJ,KAAK,GAAG,IAAImB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEpD,IAAIU,QAAQ,GAAG,EAAE;IACjB,QAAQnC,MAAM;MACZ,KAAK,OAAO;QACVmC,QAAQ,GAAGzC,YAAY,CAACM,MAAM,CAACgC,GAAG,IAAIA,GAAG,CAACV,IAAI,KAAKlB,KAAK,CAAC;QACzD;MACF,KAAK,UAAU;QACb+B,QAAQ,GAAGzC,YAAY,CAACM,MAAM,CAACgC,GAAG,IAAIA,GAAG,CAACV,IAAI,IAAIlB,KAAK,CAAC;QACxD;MACF;QACE+B,QAAQ,GAAGzC,YAAY;IAC3B;;IAEA;IACAyC,QAAQ,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAID,CAAC,CAACf,IAAI,KAAKgB,CAAC,CAAChB,IAAI,EAAE;QACrB,OAAOe,CAAC,CAACX,IAAI,CAACa,aAAa,CAACD,CAAC,CAACZ,IAAI,CAAC;MACrC;MACA,OAAOW,CAAC,CAACf,IAAI,CAACiB,aAAa,CAACD,CAAC,CAAChB,IAAI,CAAC;IACrC,CAAC,CAAC;IAEFzB,uBAAuB,CAACsC,QAAQ,CAAC;EACnC,CAAC;EAED,MAAMK,uBAAuB,GAAG,MAAAA,CAAOC,aAAa,EAAEC,SAAS,KAAK;IAClE,IAAI;MACF,MAAMzD,eAAe,CAAC0D,MAAM,CAACF,aAAa,EAAE;QAAEd,MAAM,EAAEe;MAAU,CAAC,CAAC;;MAElE;MACA/C,eAAe,CAACiD,IAAI,IAClBA,IAAI,CAACC,GAAG,CAACb,GAAG,IACVA,GAAG,CAACb,EAAE,KAAKsB,aAAa,GACpB;QAAE,GAAGT,GAAG;QAAEL,MAAM,EAAEe;MAAU,CAAC,GAC7BV,GACN,CACF,CAAC;IACH,CAAC,CAAC,OAAOlB,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,oCAAoC,EAAEF,GAAG,CAAC;;MAExD;MACAnB,eAAe,CAACiD,IAAI,IAClBA,IAAI,CAACC,GAAG,CAACb,GAAG,IACVA,GAAG,CAACb,EAAE,KAAKsB,aAAa,GACpB;QAAE,GAAGT,GAAG;QAAEL,MAAM,EAAEe;MAAU,CAAC,GAC7BV,GACN,CACF,CAAC;IACH;EACF,CAAC;EAED,IAAI,CAACxC,QAAQ,EAAE;IACb,OAAO,IAAI,CAAC,CAAC;EACf;EAEA,IAAIM,OAAO,EAAE;IACX,oBACEV,OAAA;MAAK0D,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtC3D,OAAA,CAACF,MAAM;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACV/D,OAAA;QAAK0D,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpD3D,OAAA;UAAK0D,SAAS,EAAC;QAAmE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE/D,OAAA;IAAK0D,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtC3D,OAAA,CAACF,MAAM;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEV/D,OAAA;MAAK0D,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAC1D3D,OAAA;QAAK0D,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB3D,OAAA;UAAI0D,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtE/D,OAAA;UAAG0D,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAC,gBAAc,EAACxD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,IAAI;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eAGN/D,OAAA;QAAK0D,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzD3D,OAAA;UAAK0D,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChD3D,OAAA;YAAK0D,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC3D,OAAA;cAAK0D,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B3D,OAAA;gBAAK0D,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,eAC9E3D,OAAA;kBAAK0D,SAAS,EAAC,oBAAoB;kBAACO,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAP,QAAA,eACzE3D,OAAA;oBAAMmE,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,oHAAoH;oBAACC,QAAQ,EAAC;kBAAS;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN/D,OAAA;cAAK0D,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9B3D,OAAA;gBAAA2D,QAAA,gBACE3D,OAAA;kBAAI0D,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpF/D,OAAA;kBAAI0D,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAE7C,KAAK,CAACE;gBAAK;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/D,OAAA;UAAK0D,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChD3D,OAAA;YAAK0D,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC3D,OAAA;cAAK0D,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B3D,OAAA;gBAAK0D,SAAS,EAAC,kEAAkE;gBAAAC,QAAA,eAC/E3D,OAAA;kBAAK0D,SAAS,EAAC,oBAAoB;kBAACO,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAP,QAAA,eACzE3D,OAAA;oBAAMmE,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,wJAAwJ;oBAACC,QAAQ,EAAC;kBAAS;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN/D,OAAA;cAAK0D,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9B3D,OAAA;gBAAA2D,QAAA,gBACE3D,OAAA;kBAAI0D,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrF/D,OAAA;kBAAI0D,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAE7C,KAAK,CAACG;gBAAQ;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/D,OAAA;UAAK0D,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChD3D,OAAA;YAAK0D,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC3D,OAAA;cAAK0D,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B3D,OAAA;gBAAK0D,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,eAChF3D,OAAA;kBAAK0D,SAAS,EAAC,oBAAoB;kBAACO,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAP,QAAA,eACzE3D,OAAA;oBAAMoE,CAAC,EAAC;kBAA+C;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN/D,OAAA;cAAK0D,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9B3D,OAAA;gBAAA2D,QAAA,gBACE3D,OAAA;kBAAI0D,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClF/D,OAAA;kBAAI0D,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAE7C,KAAK,CAACI;gBAAK;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/D,OAAA;QAAK0D,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5C3D,OAAA;UAAK0D,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjD3D,OAAA;YAAK0D,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD3D,OAAA;cAAI0D,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnE/D,OAAA;cAAK0D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B3D,OAAA;gBACEsE,OAAO,EAAEA,CAAA,KAAMzD,SAAS,CAAC,OAAO,CAAE;gBAClC6C,SAAS,EAAE,yCACT9C,MAAM,KAAK,OAAO,GACd,2BAA2B,GAC3B,6CAA6C,EAChD;gBAAA+C,QAAA,EACJ;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/D,OAAA;gBACEsE,OAAO,EAAEA,CAAA,KAAMzD,SAAS,CAAC,UAAU,CAAE;gBACrC6C,SAAS,EAAE,yCACT9C,MAAM,KAAK,UAAU,GACjB,2BAA2B,GAC3B,6CAA6C,EAChD;gBAAA+C,QAAA,EACJ;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/D,OAAA;gBACEsE,OAAO,EAAEA,CAAA,KAAMzD,SAAS,CAAC,KAAK,CAAE;gBAChC6C,SAAS,EAAE,yCACT9C,MAAM,KAAK,KAAK,GACZ,2BAA2B,GAC3B,6CAA6C,EAChD;gBAAA+C,QAAA,EACJ;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/D,OAAA;UAAK0D,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EACtCnD,oBAAoB,CAACsC,MAAM,KAAK,CAAC,gBAChC9C,OAAA;YAAK0D,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAErD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GAENvD,oBAAoB,CAACiD,GAAG,CAAEc,WAAW,iBACnCvE,OAAA;YAA0B0D,SAAS,EAAC,WAAW;YAAAC,QAAA,eAC7C3D,OAAA;cAAK0D,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD3D,OAAA;gBAAK0D,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrB3D,OAAA;kBAAK0D,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C3D,OAAA;oBAAA2D,QAAA,gBACE3D,OAAA;sBAAI0D,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC9CY,WAAW,CAACvC;oBAAW;sBAAA4B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CAAC,eACL/D,OAAA;sBAAG0D,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEY,WAAW,CAACtC;oBAAY;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC,eACN/D,OAAA;oBAAK0D,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,eACpC3D,OAAA;sBAAA2D,QAAA,GAAIY,WAAW,CAACrC,IAAI,EAAC,MAAI,EAACqC,WAAW,CAACjC,IAAI;oBAAA;sBAAAsB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EACLQ,WAAW,CAAC/B,KAAK,iBAChBxC,OAAA;kBAAG0D,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAEY,WAAW,CAAC/B;gBAAK;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CACjE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN/D,OAAA;gBAAK0D,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C3D,OAAA;kBAAM0D,SAAS,EAAE,4DACfa,WAAW,CAAChC,MAAM,KAAK,WAAW,GAC9B,+BAA+B,GAC/BgC,WAAW,CAAChC,MAAM,KAAK,WAAW,GAClC,6BAA6B,GAC7B,yBAAyB,EAC5B;kBAAAoB,QAAA,EACAY,WAAW,CAAChC;gBAAM;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,EAENQ,WAAW,CAAChC,MAAM,KAAK,WAAW,iBACjCvC,OAAA;kBAAK0D,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B3D,OAAA;oBACEsE,OAAO,EAAEA,CAAA,KAAMlB,uBAAuB,CAACmB,WAAW,CAACxC,EAAE,EAAE,WAAW,CAAE;oBACpE2B,SAAS,EAAC,kFAAkF;oBAAAC,QAAA,EAC7F;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT/D,OAAA;oBACEsE,OAAO,EAAEA,CAAA,KAAMlB,uBAAuB,CAACmB,WAAW,CAACxC,EAAE,EAAE,WAAW,CAAE;oBACpE2B,SAAS,EAAC,8EAA8E;oBAAAC,QAAA,EACzF;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GA/CEQ,WAAW,CAACxC,EAAE;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgDnB,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7D,EAAA,CA3VID,eAAe;EAAA,QACQL,OAAO,EACjBD,WAAW;AAAA;AAAA6E,EAAA,GAFxBvE,eAAe;AA6VrB,eAAeA,eAAe;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}