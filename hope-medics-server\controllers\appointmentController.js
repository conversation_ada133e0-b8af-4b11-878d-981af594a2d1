const Appointment = require('../models/Appointment');

const appointmentController = {
  async getAllAppointments(req, res) {
    try {
      const appointments = await Appointment.findAll();
      
      res.json({
        success: true,
        data: appointments
      });
    } catch (error) {
      console.error('Get appointments error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  async getAppointmentById(req, res) {
    try {
      const { id } = req.params;
      const appointment = await Appointment.findById(id);
      
      if (!appointment) {
        return res.status(404).json({
          success: false,
          message: 'Appointment not found'
        });
      }

      res.json({
        success: true,
        data: appointment
      });
    } catch (error) {
      console.error('Get appointment error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  async getAppointmentsByPatient(req, res) {
    try {
      const { patientId } = req.params;
      const appointments = await Appointment.findByPatient(patientId);
      
      res.json({
        success: true,
        data: appointments
      });
    } catch (error) {
      console.error('Get patient appointments error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  async getAppointmentsByDoctor(req, res) {
    try {
      const { doctorId } = req.params;
      const appointments = await Appointment.findByDoctor(doctorId);
      
      res.json({
        success: true,
        data: appointments
      });
    } catch (error) {
      console.error('Get doctor appointments error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  async createAppointment(req, res) {
    try {
      const { 
        hospitalId, 
        doctorId, 
        date, 
        time, 
        notes 
      } = req.body;

      // Validation
      if (!hospitalId || !doctorId || !date || !time) {
        return res.status(400).json({
          success: false,
          message: 'Hospital, doctor, date, and time are required'
        });
      }

      const appointment = await Appointment.create({
        patientId: req.userId,
        hospitalId,
        doctorId,
        date,
        time,
        notes,
        status: 'Scheduled'
      });

      res.status(201).json({
        success: true,
        message: 'Appointment booked successfully',
        data: appointment
      });
    } catch (error) {
      console.error('Create appointment error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  async manualBooking(req, res) {
    try {
      const { 
        hospitalId, 
        doctorId, 
        date, 
        time, 
        notes,
        patientName,
        patientEmail,
        patientPhone
      } = req.body;

      // Validation
      if (!hospitalId || !doctorId || !date || !time || !patientName || !patientEmail) {
        return res.status(400).json({
          success: false,
          message: 'Hospital, doctor, date, time, patient name, and email are required'
        });
      }

      const appointment = await Appointment.create({
        patientId: null, // Walk-in patient
        hospitalId,
        doctorId,
        date,
        time,
        notes,
        patientName,
        patientEmail,
        patientPhone,
        status: 'Scheduled'
      });

      res.status(201).json({
        success: true,
        message: 'Walk-in appointment booked successfully',
        data: appointment
      });
    } catch (error) {
      console.error('Manual booking error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  async updateAppointment(req, res) {
    try {
      const { id } = req.params;
      const { date, time, status, notes } = req.body;

      const appointment = await Appointment.findById(id);
      if (!appointment) {
        return res.status(404).json({
          success: false,
          message: 'Appointment not found'
        });
      }

      const updatedAppointment = await Appointment.update(id, {
        date,
        time,
        status,
        notes
      });

      res.json({
        success: true,
        message: 'Appointment updated successfully',
        data: updatedAppointment
      });
    } catch (error) {
      console.error('Update appointment error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  async updateAppointmentStatus(req, res) {
    try {
      const { id } = req.params;
      const { status } = req.body;

      if (!status) {
        return res.status(400).json({
          success: false,
          message: 'Status is required'
        });
      }

      const appointment = await Appointment.findById(id);
      if (!appointment) {
        return res.status(404).json({
          success: false,
          message: 'Appointment not found'
        });
      }

      const updatedAppointment = await Appointment.updateStatus(id, status);

      res.json({
        success: true,
        message: 'Appointment status updated successfully',
        data: updatedAppointment
      });
    } catch (error) {
      console.error('Update appointment status error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  async deleteAppointment(req, res) {
    try {
      const { id } = req.params;

      const appointment = await Appointment.findById(id);
      if (!appointment) {
        return res.status(404).json({
          success: false,
          message: 'Appointment not found'
        });
      }

      const deleted = await Appointment.delete(id);
      if (!deleted) {
        return res.status(500).json({
          success: false,
          message: 'Failed to delete appointment'
        });
      }

      res.json({
        success: true,
        message: 'Appointment cancelled successfully'
      });
    } catch (error) {
      console.error('Delete appointment error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
};

module.exports = appointmentController;
