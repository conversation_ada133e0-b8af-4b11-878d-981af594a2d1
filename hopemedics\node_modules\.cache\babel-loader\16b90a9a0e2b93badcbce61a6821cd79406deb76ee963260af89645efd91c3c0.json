{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\hopeMedics\\\\hopemedics\\\\src\\\\components\\\\DoctorCard.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DoctorCard = ({\n  doctor,\n  onSelect,\n  isSelected\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `bg-white rounded-lg shadow-md p-4 cursor-pointer transition-all duration-200 ${isSelected ? 'ring-2 ring-primary-500 bg-primary-50' : 'hover:shadow-lg hover:bg-gray-50'}`,\n    onClick: () => onSelect && onSelect(doctor),\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-shrink-0\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-primary-600 font-semibold text-lg\",\n            children: doctor.name.charAt(0)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 min-w-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 truncate\",\n          children: [\"Dr. \", doctor.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-600\",\n          children: doctor.specialty\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), isSelected && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-shrink-0\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-4 h-4 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = DoctorCard;\nexport default DoctorCard;\nvar _c;\n$RefreshReg$(_c, \"DoctorCard\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "doctor", "onSelect", "isSelected", "className", "onClick", "children", "name", "char<PERSON>t", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "specialty", "fill", "viewBox", "fillRule", "d", "clipRule", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/hopeMedics/hopemedics/src/components/DoctorCard.js"], "sourcesContent": ["import React from 'react';\n\nconst DoctorCard = ({ doctor, onSelect, isSelected }) => {\n  return (\n    <div \n      className={`bg-white rounded-lg shadow-md p-4 cursor-pointer transition-all duration-200 ${\n        isSelected \n          ? 'ring-2 ring-primary-500 bg-primary-50' \n          : 'hover:shadow-lg hover:bg-gray-50'\n      }`}\n      onClick={() => onSelect && onSelect(doctor)}\n    >\n      <div className=\"flex items-center space-x-4\">\n        <div className=\"flex-shrink-0\">\n          <div className=\"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center\">\n            <span className=\"text-primary-600 font-semibold text-lg\">\n              {doctor.name.charAt(0)}\n            </span>\n          </div>\n        </div>\n        \n        <div className=\"flex-1 min-w-0\">\n          <h3 className=\"text-lg font-semibold text-gray-900 truncate\">\n            Dr. {doctor.name}\n          </h3>\n          <p className=\"text-sm text-gray-600\">{doctor.specialty}</p>\n        </div>\n        \n        {isSelected && (\n          <div className=\"flex-shrink-0\">\n            <div className=\"w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center\">\n              <svg className=\"w-4 h-4 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default DoctorCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAC;EAAEC,MAAM;EAAEC,QAAQ;EAAEC;AAAW,CAAC,KAAK;EACvD,oBACEJ,OAAA;IACEK,SAAS,EAAE,gFACTD,UAAU,GACN,uCAAuC,GACvC,kCAAkC,EACrC;IACHE,OAAO,EAAEA,CAAA,KAAMH,QAAQ,IAAIA,QAAQ,CAACD,MAAM,CAAE;IAAAK,QAAA,eAE5CP,OAAA;MAAKK,SAAS,EAAC,6BAA6B;MAAAE,QAAA,gBAC1CP,OAAA;QAAKK,SAAS,EAAC,eAAe;QAAAE,QAAA,eAC5BP,OAAA;UAAKK,SAAS,EAAC,wEAAwE;UAAAE,QAAA,eACrFP,OAAA;YAAMK,SAAS,EAAC,wCAAwC;YAAAE,QAAA,EACrDL,MAAM,CAACM,IAAI,CAACC,MAAM,CAAC,CAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENb,OAAA;QAAKK,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7BP,OAAA;UAAIK,SAAS,EAAC,8CAA8C;UAAAE,QAAA,GAAC,MACvD,EAACL,MAAM,CAACM,IAAI;QAAA;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACLb,OAAA;UAAGK,SAAS,EAAC,uBAAuB;UAAAE,QAAA,EAAEL,MAAM,CAACY;QAAS;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,EAELT,UAAU,iBACTJ,OAAA;QAAKK,SAAS,EAAC,eAAe;QAAAE,QAAA,eAC5BP,OAAA;UAAKK,SAAS,EAAC,sEAAsE;UAAAE,QAAA,eACnFP,OAAA;YAAKK,SAAS,EAAC,oBAAoB;YAACU,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAT,QAAA,eACzEP,OAAA;cAAMiB,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,oHAAoH;cAACC,QAAQ,EAAC;YAAS;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACO,EAAA,GAtCInB,UAAU;AAwChB,eAAeA,UAAU;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}