{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\hopeMedics\\\\hopemedics\\\\src\\\\components\\\\Navbar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const {\n    user,\n    logout,\n    isAuthenticated\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n  const getDashboardLink = () => {\n    if (!user) return null;\n    switch (user.role) {\n      case 'admin':\n        return '/admin';\n      case 'doctor':\n        return '/doctor';\n      case 'receptionist':\n        return '/reception';\n      default:\n        return '/';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"bg-primary-600 text-white shadow-lg\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"flex-shrink-0 flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold\",\n              children: \"Hope Medics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:block ml-10\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-baseline space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/\",\n                className: \"hover:bg-primary-700 px-3 py-2 rounded-md text-sm font-medium\",\n                children: \"Hospitals\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 17\n              }, this), isAuthenticated && /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/book\",\n                className: \"hover:bg-primary-700 px-3 py-2 rounded-md text-sm font-medium\",\n                children: \"Book Appointment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: isAuthenticated ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: [\"Welcome, \", user.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 17\n            }, this), getDashboardLink() && /*#__PURE__*/_jsxDEV(Link, {\n              to: getDashboardLink(),\n              className: \"bg-primary-700 hover:bg-primary-800 px-3 py-2 rounded-md text-sm font-medium\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleLogout,\n              className: \"bg-red-600 hover:bg-red-700 px-3 py-2 rounded-md text-sm font-medium\",\n              children: \"Logout\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"hover:bg-primary-700 px-3 py-2 rounded-md text-sm font-medium\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"bg-secondary-600 hover:bg-secondary-700 px-3 py-2 rounded-md text-sm font-medium\",\n              children: \"Register\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"no8SRGBJjVU8Mxe3uwjsHY/Wmig=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "Link", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "_s", "user", "logout", "isAuthenticated", "navigate", "handleLogout", "getDashboardLink", "role", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/hopeMedics/hopemedics/src/components/Navbar.js"], "sourcesContent": ["import React from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\n\nconst Navbar = () => {\n  const { user, logout, isAuthenticated } = useAuth();\n  const navigate = useNavigate();\n\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n\n  const getDashboardLink = () => {\n    if (!user) return null;\n    \n    switch (user.role) {\n      case 'admin':\n        return '/admin';\n      case 'doctor':\n        return '/doctor';\n      case 'receptionist':\n        return '/reception';\n      default:\n        return '/';\n    }\n  };\n\n  return (\n    <nav className=\"bg-primary-600 text-white shadow-lg\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link to=\"/\" className=\"flex-shrink-0 flex items-center\">\n              <h1 className=\"text-xl font-bold\"><PERSON> Medics</h1>\n            </Link>\n            <div className=\"hidden md:block ml-10\">\n              <div className=\"flex items-baseline space-x-4\">\n                <Link\n                  to=\"/\"\n                  className=\"hover:bg-primary-700 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Hospitals\n                </Link>\n                {isAuthenticated && (\n                  <Link\n                    to=\"/book\"\n                    className=\"hover:bg-primary-700 px-3 py-2 rounded-md text-sm font-medium\"\n                  >\n                    Book Appointment\n                  </Link>\n                )}\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-4\">\n            {isAuthenticated ? (\n              <>\n                <span className=\"text-sm\">Welcome, {user.name}</span>\n                {getDashboardLink() && (\n                  <Link\n                    to={getDashboardLink()}\n                    className=\"bg-primary-700 hover:bg-primary-800 px-3 py-2 rounded-md text-sm font-medium\"\n                  >\n                    Dashboard\n                  </Link>\n                )}\n                <button\n                  onClick={handleLogout}\n                  className=\"bg-red-600 hover:bg-red-700 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Logout\n                </button>\n              </>\n            ) : (\n              <>\n                <Link\n                  to=\"/login\"\n                  className=\"hover:bg-primary-700 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Login\n                </Link>\n                <Link\n                  to=\"/register\"\n                  className=\"bg-secondary-600 hover:bg-secondary-700 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Register\n                </Link>\n              </>\n            )}\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM;IAAEC,IAAI;IAAEC,MAAM;IAAEC;EAAgB,CAAC,GAAGT,OAAO,CAAC,CAAC;EACnD,MAAMU,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,MAAMY,YAAY,GAAGA,CAAA,KAAM;IACzBH,MAAM,CAAC,CAAC;IACRE,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,MAAME,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACL,IAAI,EAAE,OAAO,IAAI;IAEtB,QAAQA,IAAI,CAACM,IAAI;MACf,KAAK,OAAO;QACV,OAAO,QAAQ;MACjB,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,cAAc;QACjB,OAAO,YAAY;MACrB;QACE,OAAO,GAAG;IACd;EACF,CAAC;EAED,oBACEX,OAAA;IAAKY,SAAS,EAAC,qCAAqC;IAAAC,QAAA,eAClDb,OAAA;MAAKY,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACrDb,OAAA;QAAKY,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxCb,OAAA;UAAKY,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCb,OAAA,CAACJ,IAAI;YAACkB,EAAE,EAAC,GAAG;YAACF,SAAS,EAAC,iCAAiC;YAAAC,QAAA,eACtDb,OAAA;cAAIY,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACPlB,OAAA;YAAKY,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eACpCb,OAAA;cAAKY,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC5Cb,OAAA,CAACJ,IAAI;gBACHkB,EAAE,EAAC,GAAG;gBACNF,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,EAC1E;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACNX,eAAe,iBACdP,OAAA,CAACJ,IAAI;gBACHkB,EAAE,EAAC,OAAO;gBACVF,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,EAC1E;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlB,OAAA;UAAKY,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EACzCN,eAAe,gBACdP,OAAA,CAAAE,SAAA;YAAAW,QAAA,gBACEb,OAAA;cAAMY,SAAS,EAAC,SAAS;cAAAC,QAAA,GAAC,WAAS,EAACR,IAAI,CAACc,IAAI;YAAA;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACpDR,gBAAgB,CAAC,CAAC,iBACjBV,OAAA,CAACJ,IAAI;cACHkB,EAAE,EAAEJ,gBAAgB,CAAC,CAAE;cACvBE,SAAS,EAAC,8EAA8E;cAAAC,QAAA,EACzF;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP,eACDlB,OAAA;cACEoB,OAAO,EAAEX,YAAa;cACtBG,SAAS,EAAC,sEAAsE;cAAAC,QAAA,EACjF;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CAAC,gBAEHlB,OAAA,CAAAE,SAAA;YAAAW,QAAA,gBACEb,OAAA,CAACJ,IAAI;cACHkB,EAAE,EAAC,QAAQ;cACXF,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAC1E;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPlB,OAAA,CAACJ,IAAI;cACHkB,EAAE,EAAC,WAAW;cACdF,SAAS,EAAC,kFAAkF;cAAAC,QAAA,EAC7F;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACP;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACd,EAAA,CA5FID,MAAM;EAAA,QACgCL,OAAO,EAChCD,WAAW;AAAA;AAAAwB,EAAA,GAFxBlB,MAAM;AA8FZ,eAAeA,MAAM;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}