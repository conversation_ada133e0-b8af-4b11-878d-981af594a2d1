{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\hopeMedics\\\\hopemedics\\\\src\\\\pages\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { hospitalsAPI, doctorsAPI, appointmentsAPI } from '../services/api';\nimport Navbar from '../components/Navbar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  const {\n    user,\n    isAdmin\n  } = useAuth();\n  const navigate = useNavigate();\n  const [stats, setStats] = useState({\n    totalHospitals: 0,\n    totalDoctors: 0,\n    totalAppointments: 0,\n    todayAppointments: 0\n  });\n  const [recentAppointments, setRecentAppointments] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    if (!isAdmin) {\n      navigate('/');\n      return;\n    }\n    fetchDashboardData();\n  }, [isAdmin, navigate]);\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n\n      // Fetch all data\n      const [hospitalsRes, doctorsRes, appointmentsRes] = await Promise.all([hospitalsAPI.getAll(), doctorsAPI.getAll(), appointmentsAPI.getAll()]);\n      const hospitals = hospitalsRes.data;\n      const doctors = doctorsRes.data;\n      const appointments = appointmentsRes.data;\n\n      // Calculate stats\n      const today = new Date().toISOString().split('T')[0];\n      const todayAppointments = appointments.filter(apt => apt.date === today);\n      setStats({\n        totalHospitals: hospitals.length,\n        totalDoctors: doctors.length,\n        totalAppointments: appointments.length,\n        todayAppointments: todayAppointments.length\n      });\n\n      // Get recent appointments (last 10)\n      const sortedAppointments = appointments.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)).slice(0, 10);\n      setRecentAppointments(sortedAppointments);\n    } catch (err) {\n      console.error('Error fetching dashboard data:', err);\n\n      // Mock data for development\n      setStats({\n        totalHospitals: 3,\n        totalDoctors: 9,\n        totalAppointments: 25,\n        todayAppointments: 5\n      });\n      setRecentAppointments(getMockAppointments());\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getMockAppointments = () => [{\n    id: 1,\n    patientName: 'John Doe',\n    doctorName: 'Dr. Sarah Johnson',\n    hospitalName: 'City General Hospital',\n    date: '2024-01-15',\n    time: '10:00',\n    status: 'Scheduled'\n  }, {\n    id: 2,\n    patientName: 'Jane Smith',\n    doctorName: 'Dr. Michael Chen',\n    hospitalName: 'City General Hospital',\n    date: '2024-01-15',\n    time: '14:30',\n    status: 'Completed'\n  }, {\n    id: 3,\n    patientName: 'Bob Wilson',\n    doctorName: 'Dr. Robert Wilson',\n    hospitalName: 'Hope Medical Center',\n    date: '2024-01-16',\n    time: '09:00',\n    status: 'Scheduled'\n  }];\n  if (!isAdmin) {\n    return null; // Will redirect\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center items-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900\",\n          children: \"Admin Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-white\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L9 5.414V17a1 1 0 102 0V5.414l5.293 5.293a1 1 0 001.414-1.414l-7-7z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 141,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"Total Hospitals\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: stats.totalHospitals\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-white\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 159,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"Total Doctors\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: stats.totalDoctors\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-white\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"Total Appointments\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: stats.totalAppointments\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-red-500 rounded-md flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-white\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"Today's Appointments\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: stats.todayAppointments\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: \"Recent Appointments\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full divide-y divide-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Patient\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Doctor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Hospital\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Date & Time\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: recentAppointments.map(appointment => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                  children: appointment.patientName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                  children: appointment.doctorName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                  children: appointment.hospitalName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                  children: [appointment.date, \" at \", appointment.time]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${appointment.status === 'Scheduled' ? 'bg-yellow-100 text-yellow-800' : appointment.status === 'Completed' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                    children: appointment.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 21\n                }, this)]\n              }, appointment.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"vw0o5e3+BIbhSQHuKP+CEm3Gzt0=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useAuth", "hospitalsAPI", "doctorsAPI", "appointmentsAPI", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "user", "isAdmin", "navigate", "stats", "setStats", "totalHospitals", "totalDoctors", "totalAppointments", "todayAppointments", "recentAppointments", "setRecentAppointments", "loading", "setLoading", "fetchDashboardData", "hospitalsRes", "doctorsRes", "appointmentsRes", "Promise", "all", "getAll", "hospitals", "data", "doctors", "appointments", "today", "Date", "toISOString", "split", "filter", "apt", "date", "length", "sortedAppointments", "sort", "a", "b", "createdAt", "slice", "err", "console", "error", "getMockAppointments", "id", "patientName", "<PERSON><PERSON><PERSON>", "hospitalName", "time", "status", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "fill", "viewBox", "d", "fillRule", "clipRule", "map", "appointment", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/hopeMedics/hopemedics/src/pages/AdminDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { hospitalsAPI, doctorsAPI, appointmentsAPI } from '../services/api';\nimport Navbar from '../components/Navbar';\n\nconst AdminDashboard = () => {\n  const { user, isAdmin } = useAuth();\n  const navigate = useNavigate();\n  \n  const [stats, setStats] = useState({\n    totalHospitals: 0,\n    totalDoctors: 0,\n    totalAppointments: 0,\n    todayAppointments: 0\n  });\n  const [recentAppointments, setRecentAppointments] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    if (!isAdmin) {\n      navigate('/');\n      return;\n    }\n    \n    fetchDashboardData();\n  }, [isAdmin, navigate]);\n\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      \n      // Fetch all data\n      const [hospitalsRes, doctorsRes, appointmentsRes] = await Promise.all([\n        hospitalsAPI.getAll(),\n        doctorsAPI.getAll(),\n        appointmentsAPI.getAll()\n      ]);\n\n      const hospitals = hospitalsRes.data;\n      const doctors = doctorsRes.data;\n      const appointments = appointmentsRes.data;\n\n      // Calculate stats\n      const today = new Date().toISOString().split('T')[0];\n      const todayAppointments = appointments.filter(apt => apt.date === today);\n\n      setStats({\n        totalHospitals: hospitals.length,\n        totalDoctors: doctors.length,\n        totalAppointments: appointments.length,\n        todayAppointments: todayAppointments.length\n      });\n\n      // Get recent appointments (last 10)\n      const sortedAppointments = appointments\n        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))\n        .slice(0, 10);\n      \n      setRecentAppointments(sortedAppointments);\n      \n    } catch (err) {\n      console.error('Error fetching dashboard data:', err);\n      \n      // Mock data for development\n      setStats({\n        totalHospitals: 3,\n        totalDoctors: 9,\n        totalAppointments: 25,\n        todayAppointments: 5\n      });\n      \n      setRecentAppointments(getMockAppointments());\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getMockAppointments = () => [\n    {\n      id: 1,\n      patientName: 'John Doe',\n      doctorName: 'Dr. Sarah Johnson',\n      hospitalName: 'City General Hospital',\n      date: '2024-01-15',\n      time: '10:00',\n      status: 'Scheduled'\n    },\n    {\n      id: 2,\n      patientName: 'Jane Smith',\n      doctorName: 'Dr. Michael Chen',\n      hospitalName: 'City General Hospital',\n      date: '2024-01-15',\n      time: '14:30',\n      status: 'Completed'\n    },\n    {\n      id: 3,\n      patientName: 'Bob Wilson',\n      doctorName: 'Dr. Robert Wilson',\n      hospitalName: 'Hope Medical Center',\n      date: '2024-01-16',\n      time: '09:00',\n      status: 'Scheduled'\n    }\n  ];\n\n  if (!isAdmin) {\n    return null; // Will redirect\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navbar />\n        <div className=\"flex justify-center items-center h-64\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n      \n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">Admin Dashboard</h1>\n          <p className=\"text-gray-600\">Welcome back, {user?.name}</p>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path d=\"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L9 5.414V17a1 1 0 102 0V5.414l5.293 5.293a1 1 0 001.414-1.414l-7-7z\"/>\n                  </svg>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Hospitals</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{stats.totalHospitals}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clipRule=\"evenodd\"/>\n                  </svg>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Doctors</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{stats.totalDoctors}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z\" clipRule=\"evenodd\"/>\n                  </svg>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Appointments</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{stats.totalAppointments}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-red-500 rounded-md flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\" clipRule=\"evenodd\"/>\n                  </svg>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Today's Appointments</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{stats.todayAppointments}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Recent Appointments */}\n        <div className=\"bg-white rounded-lg shadow-md\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <h2 className=\"text-lg font-medium text-gray-900\">Recent Appointments</h2>\n          </div>\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Patient\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Doctor\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Hospital\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Date & Time\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Status\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {recentAppointments.map((appointment) => (\n                  <tr key={appointment.id}>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                      {appointment.patientName}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {appointment.doctorName}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {appointment.hospitalName}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {appointment.date} at {appointment.time}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                        appointment.status === 'Scheduled' \n                          ? 'bg-yellow-100 text-yellow-800'\n                          : appointment.status === 'Completed'\n                          ? 'bg-green-100 text-green-800'\n                          : 'bg-red-100 text-red-800'\n                      }`}>\n                        {appointment.status}\n                      </span>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,YAAY,EAAEC,UAAU,EAAEC,eAAe,QAAQ,iBAAiB;AAC3E,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGV,OAAO,CAAC,CAAC;EACnC,MAAMW,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC;IACjCiB,cAAc,EAAE,CAAC;IACjBC,YAAY,EAAE,CAAC;IACfC,iBAAiB,EAAE,CAAC;IACpBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,IAAI,CAACY,OAAO,EAAE;MACZC,QAAQ,CAAC,GAAG,CAAC;MACb;IACF;IAEAW,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACZ,OAAO,EAAEC,QAAQ,CAAC,CAAC;EAEvB,MAAMW,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFD,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM,CAACE,YAAY,EAAEC,UAAU,EAAEC,eAAe,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACpE1B,YAAY,CAAC2B,MAAM,CAAC,CAAC,EACrB1B,UAAU,CAAC0B,MAAM,CAAC,CAAC,EACnBzB,eAAe,CAACyB,MAAM,CAAC,CAAC,CACzB,CAAC;MAEF,MAAMC,SAAS,GAAGN,YAAY,CAACO,IAAI;MACnC,MAAMC,OAAO,GAAGP,UAAU,CAACM,IAAI;MAC/B,MAAME,YAAY,GAAGP,eAAe,CAACK,IAAI;;MAEzC;MACA,MAAMG,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpD,MAAMnB,iBAAiB,GAAGe,YAAY,CAACK,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAKN,KAAK,CAAC;MAExEpB,QAAQ,CAAC;QACPC,cAAc,EAAEe,SAAS,CAACW,MAAM;QAChCzB,YAAY,EAAEgB,OAAO,CAACS,MAAM;QAC5BxB,iBAAiB,EAAEgB,YAAY,CAACQ,MAAM;QACtCvB,iBAAiB,EAAEA,iBAAiB,CAACuB;MACvC,CAAC,CAAC;;MAEF;MACA,MAAMC,kBAAkB,GAAGT,YAAY,CACpCU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIV,IAAI,CAACU,CAAC,CAACC,SAAS,CAAC,GAAG,IAAIX,IAAI,CAACS,CAAC,CAACE,SAAS,CAAC,CAAC,CAC7DC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;MAEf3B,qBAAqB,CAACsB,kBAAkB,CAAC;IAE3C,CAAC,CAAC,OAAOM,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAAEF,GAAG,CAAC;;MAEpD;MACAlC,QAAQ,CAAC;QACPC,cAAc,EAAE,CAAC;QACjBC,YAAY,EAAE,CAAC;QACfC,iBAAiB,EAAE,EAAE;QACrBC,iBAAiB,EAAE;MACrB,CAAC,CAAC;MAEFE,qBAAqB,CAAC+B,mBAAmB,CAAC,CAAC,CAAC;IAC9C,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6B,mBAAmB,GAAGA,CAAA,KAAM,CAChC;IACEC,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,UAAU;IACvBC,UAAU,EAAE,mBAAmB;IAC/BC,YAAY,EAAE,uBAAuB;IACrCf,IAAI,EAAE,YAAY;IAClBgB,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,YAAY;IACzBC,UAAU,EAAE,kBAAkB;IAC9BC,YAAY,EAAE,uBAAuB;IACrCf,IAAI,EAAE,YAAY;IAClBgB,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,YAAY;IACzBC,UAAU,EAAE,mBAAmB;IAC/BC,YAAY,EAAE,qBAAqB;IACnCf,IAAI,EAAE,YAAY;IAClBgB,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE;EACV,CAAC,CACF;EAED,IAAI,CAAC9C,OAAO,EAAE;IACZ,OAAO,IAAI,CAAC,CAAC;EACf;EAEA,IAAIU,OAAO,EAAE;IACX,oBACEd,OAAA;MAAKmD,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtCpD,OAAA,CAACF,MAAM;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVxD,OAAA;QAAKmD,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpDpD,OAAA;UAAKmD,SAAS,EAAC;QAAmE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACExD,OAAA;IAAKmD,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtCpD,OAAA,CAACF,MAAM;MAAAuD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEVxD,OAAA;MAAKmD,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAC1DpD,OAAA;QAAKmD,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBpD,OAAA;UAAImD,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrExD,OAAA;UAAGmD,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAC,gBAAc,EAACjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,IAAI;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eAGNxD,OAAA;QAAKmD,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBACxEpD,OAAA;UAAKmD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChDpD,OAAA;YAAKmD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCpD,OAAA;cAAKmD,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BpD,OAAA;gBAAKmD,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,eAC9EpD,OAAA;kBAAKmD,SAAS,EAAC,oBAAoB;kBAACO,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAP,QAAA,eACzEpD,OAAA;oBAAM4D,CAAC,EAAC;kBAA4H;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxD,OAAA;cAAKmD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BpD,OAAA;gBAAAoD,QAAA,gBACEpD,OAAA;kBAAImD,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/ExD,OAAA;kBAAImD,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAE9C,KAAK,CAACE;gBAAc;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxD,OAAA;UAAKmD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChDpD,OAAA;YAAKmD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCpD,OAAA;cAAKmD,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BpD,OAAA;gBAAKmD,SAAS,EAAC,kEAAkE;gBAAAC,QAAA,eAC/EpD,OAAA;kBAAKmD,SAAS,EAAC,oBAAoB;kBAACO,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAP,QAAA,eACzEpD,OAAA;oBAAM6D,QAAQ,EAAC,SAAS;oBAACD,CAAC,EAAC,qDAAqD;oBAACE,QAAQ,EAAC;kBAAS;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxD,OAAA;cAAKmD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BpD,OAAA;gBAAAoD,QAAA,gBACEpD,OAAA;kBAAImD,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7ExD,OAAA;kBAAImD,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAE9C,KAAK,CAACG;gBAAY;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxD,OAAA;UAAKmD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChDpD,OAAA;YAAKmD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCpD,OAAA;cAAKmD,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BpD,OAAA;gBAAKmD,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,eAChFpD,OAAA;kBAAKmD,SAAS,EAAC,oBAAoB;kBAACO,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAP,QAAA,eACzEpD,OAAA;oBAAM6D,QAAQ,EAAC,SAAS;oBAACD,CAAC,EAAC,wJAAwJ;oBAACE,QAAQ,EAAC;kBAAS;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxD,OAAA;cAAKmD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BpD,OAAA;gBAAAoD,QAAA,gBACEpD,OAAA;kBAAImD,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClFxD,OAAA;kBAAImD,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAE9C,KAAK,CAACI;gBAAiB;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxD,OAAA;UAAKmD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChDpD,OAAA;YAAKmD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCpD,OAAA;cAAKmD,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BpD,OAAA;gBAAKmD,SAAS,EAAC,gEAAgE;gBAAAC,QAAA,eAC7EpD,OAAA;kBAAKmD,SAAS,EAAC,oBAAoB;kBAACO,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAP,QAAA,eACzEpD,OAAA;oBAAM6D,QAAQ,EAAC,SAAS;oBAACD,CAAC,EAAC,oHAAoH;oBAACE,QAAQ,EAAC;kBAAS;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxD,OAAA;cAAKmD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BpD,OAAA;gBAAAoD,QAAA,gBACEpD,OAAA;kBAAImD,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpFxD,OAAA;kBAAImD,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAE9C,KAAK,CAACK;gBAAiB;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxD,OAAA;QAAKmD,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5CpD,OAAA;UAAKmD,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjDpD,OAAA;YAAImD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eACNxD,OAAA;UAAKmD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BpD,OAAA;YAAOmD,SAAS,EAAC,qCAAqC;YAAAC,QAAA,gBACpDpD,OAAA;cAAOmD,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3BpD,OAAA;gBAAAoD,QAAA,gBACEpD,OAAA;kBAAImD,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLxD,OAAA;kBAAImD,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLxD,OAAA;kBAAImD,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLxD,OAAA;kBAAImD,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLxD,OAAA;kBAAImD,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRxD,OAAA;cAAOmD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EACjDxC,kBAAkB,CAACmD,GAAG,CAAEC,WAAW,iBAClChE,OAAA;gBAAAoD,QAAA,gBACEpD,OAAA;kBAAImD,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,EAC1EY,WAAW,CAAClB;gBAAW;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACLxD,OAAA;kBAAImD,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DY,WAAW,CAACjB;gBAAU;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACLxD,OAAA;kBAAImD,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DY,WAAW,CAAChB;gBAAY;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACLxD,OAAA;kBAAImD,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,GAC9DY,WAAW,CAAC/B,IAAI,EAAC,MAAI,EAAC+B,WAAW,CAACf,IAAI;gBAAA;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC,eACLxD,OAAA;kBAAImD,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzCpD,OAAA;oBAAMmD,SAAS,EAAE,4DACfa,WAAW,CAACd,MAAM,KAAK,WAAW,GAC9B,+BAA+B,GAC/Bc,WAAW,CAACd,MAAM,KAAK,WAAW,GAClC,6BAA6B,GAC7B,yBAAyB,EAC5B;oBAAAE,QAAA,EACAY,WAAW,CAACd;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA,GAvBEQ,WAAW,CAACnB,EAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwBnB,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtD,EAAA,CAvQID,cAAc;EAAA,QACQP,OAAO,EAChBD,WAAW;AAAA;AAAAwE,EAAA,GAFxBhE,cAAc;AAyQpB,eAAeA,cAAc;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}